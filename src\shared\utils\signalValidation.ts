/**
 * Validation utilities for Signal Engine and Strategy Factory
 * Provides comprehensive validation functions for signal engine operations
 */

import type {
  StrategyConfig,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig,
  StrategyValidationResult,
  IndicatorSignalRules,
  SignalCondition,
  IndicatorWeight,
  TradingSignal,
  StrategyCombinationLogic,
  GeneratedSignal
} from '../types/signals'
import {
  VALIDATION_CONSTRAINTS,
  SIGNAL_ENGINE_ERROR_MESSAGES,
  TRADING_SIGNALS,
  COMBINATION_LOGIC,
  SIGNAL_CONDITION_TYPES,
  COMPARISON_OPERATORS,
  CROSSOVER_DIRECTIONS
} from '../constants/signals'

/**
 * Custom validation error class
 */
export class SignalValidationError extends Error {
  constructor(
    message: string,
    public readonly field?: string,
    public readonly code?: string
  ) {
    super(message)
    this.name = 'SignalValidationError'
  }
}

/**
 * Validates a strategy configuration
 * @param config - Strategy configuration to validate
 * @returns Validation result with errors and warnings
 */
export function validateStrategyConfig(config: StrategyConfig): StrategyValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    // Validate base configuration
    validateBaseStrategyConfig(config, errors, warnings)

    // Validate specific configuration type
    if (isSingleIndicatorStrategy(config)) {
      validateSingleIndicatorStrategy(config, errors, warnings)
    } else if (isMultiIndicatorStrategy(config)) {
      validateMultiIndicatorStrategy(config, errors, warnings)
    } else {
      errors.push('Invalid strategy configuration type')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  } catch (error) {
    errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    return {
      isValid: false,
      errors,
      warnings
    }
  }
}

/**
 * Validates base strategy configuration
 */
function validateBaseStrategyConfig(
  config: StrategyConfig,
  errors: string[],
  warnings: string[]
): void {
  // Validate name
  if (!config.name || typeof config.name !== 'string') {
    errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_STRATEGY_CONFIG + ': name is required')
  } else if (config.name.length < VALIDATION_CONSTRAINTS.MIN_STRATEGY_NAME_LENGTH) {
    errors.push(
      `Strategy name must be at least ${VALIDATION_CONSTRAINTS.MIN_STRATEGY_NAME_LENGTH} characters`
    )
  } else if (config.name.length > VALIDATION_CONSTRAINTS.MAX_STRATEGY_NAME_LENGTH) {
    errors.push(
      `Strategy name must not exceed ${VALIDATION_CONSTRAINTS.MAX_STRATEGY_NAME_LENGTH} characters`
    )
  }

  // Validate confidence threshold
  if (config.minConfidence !== undefined) {
    if (
      typeof config.minConfidence !== 'number' ||
      config.minConfidence < VALIDATION_CONSTRAINTS.MIN_CONFIDENCE ||
      config.minConfidence > VALIDATION_CONSTRAINTS.MAX_CONFIDENCE
    ) {
      errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_CONFIDENCE)
    }
  }

  // Validate signal history size
  if (config.maxSignalHistory !== undefined) {
    if (
      typeof config.maxSignalHistory !== 'number' ||
      config.maxSignalHistory < VALIDATION_CONSTRAINTS.MIN_SIGNAL_HISTORY ||
      config.maxSignalHistory > VALIDATION_CONSTRAINTS.MAX_SIGNAL_HISTORY
    ) {
      errors.push(
        `Signal history size must be between ${VALIDATION_CONSTRAINTS.MIN_SIGNAL_HISTORY} and ${VALIDATION_CONSTRAINTS.MAX_SIGNAL_HISTORY}`
      )
    }
  }

  // Validate boolean flags
  if (config.enableRealTime !== undefined && typeof config.enableRealTime !== 'boolean') {
    errors.push('enableRealTime must be a boolean value')
  }

  if (config.validateInputs !== undefined && typeof config.validateInputs !== 'boolean') {
    errors.push('validateInputs must be a boolean value')
  }
}

/**
 * Validates single indicator strategy configuration
 */
function validateSingleIndicatorStrategy(
  config: SingleIndicatorStrategyConfig,
  errors: string[],
  warnings: string[]
): void {
  // Validate indicator name
  if (!config.indicator || typeof config.indicator !== 'string') {
    errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_INDICATOR + ': indicator name is required')
  }

  // Validate indicator configuration
  if (!config.indicatorConfig || typeof config.indicatorConfig !== 'object') {
    errors.push('Indicator configuration is required')
  } else {
    validateIndicatorConfig(config.indicatorConfig, errors, warnings)
  }

  // Validate signal rules
  if (!config.signalRules) {
    errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_SIGNAL_RULES + ': signal rules are required')
  } else {
    validateSignalRules(config.signalRules, errors, warnings)
  }
}

/**
 * Validates multi-indicator strategy configuration
 */
function validateMultiIndicatorStrategy(
  config: MultiIndicatorStrategyConfig,
  errors: string[],
  warnings: string[]
): void {
  // Validate indicators array
  if (!Array.isArray(config.indicators) || config.indicators.length === 0) {
    errors.push('Indicators array is required and must not be empty')
  } else if (config.indicators.length < VALIDATION_CONSTRAINTS.MIN_INDICATORS_MULTI_STRATEGY) {
    errors.push(
      `Multi-indicator strategy requires at least ${VALIDATION_CONSTRAINTS.MIN_INDICATORS_MULTI_STRATEGY} indicators`
    )
  } else if (config.indicators.length > VALIDATION_CONSTRAINTS.MAX_INDICATORS_PER_STRATEGY) {
    errors.push(
      `Strategy cannot have more than ${VALIDATION_CONSTRAINTS.MAX_INDICATORS_PER_STRATEGY} indicators`
    )
  }

  // Validate indicator names
  config.indicators.forEach((indicator, index) => {
    if (!indicator || typeof indicator !== 'string') {
      errors.push(`Invalid indicator name at index ${index}`)
    }
  })

  // Validate indicator configurations
  if (!config.indicatorConfigs || typeof config.indicatorConfigs !== 'object') {
    errors.push('Indicator configurations are required')
  } else {
    config.indicators.forEach((indicator) => {
      if (!config.indicatorConfigs[indicator]) {
        errors.push(`Missing configuration for indicator: ${indicator}`)
      } else {
        validateIndicatorConfig(config.indicatorConfigs[indicator], errors, warnings)
      }
    })
  }

  // Validate signal rules
  if (!config.signalRules || typeof config.signalRules !== 'object') {
    errors.push('Signal rules are required')
  } else {
    config.indicators.forEach((indicator) => {
      if (!config.signalRules[indicator]) {
        errors.push(`Missing signal rules for indicator: ${indicator}`)
      } else {
        validateSignalRules(config.signalRules[indicator], errors, warnings)
      }
    })
  }

  // Validate combination logic
  if (!isValidCombinationLogic(config.combinationLogic)) {
    errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_COMBINATION_LOGIC)
  }

  // Validate weights for weighted strategies
  if (config.combinationLogic === COMBINATION_LOGIC.WEIGHTED) {
    if (!config.weights || !Array.isArray(config.weights)) {
      errors.push('Weights are required for weighted combination logic')
    } else {
      validateIndicatorWeights(config.weights, config.indicators, errors, warnings)
    }
  }
}

/**
 * Validates indicator configuration
 */
function validateIndicatorConfig(
  config: Record<string, unknown>,
  errors: string[],
  warnings: string[]
): void {
  // Validate period
  if (config.period !== undefined) {
    if (typeof config.period !== 'number' || config.period < 1) {
      errors.push('Indicator period must be a positive number')
    }
  }

  // Add more indicator-specific validations as needed
}

/**
 * Validates signal rules
 */
function validateSignalRules(
  rules: IndicatorSignalRules,
  errors: string[],
  warnings: string[]
): void {
  // Validate buy conditions
  if (!Array.isArray(rules.buyConditions) || rules.buyConditions.length === 0) {
    errors.push('Buy conditions are required and must not be empty')
  } else {
    rules.buyConditions.forEach((condition, index) => {
      validateSignalCondition(condition, `buyConditions[${index}]`, errors, warnings)
    })
  }

  // Validate sell conditions
  if (!Array.isArray(rules.sellConditions) || rules.sellConditions.length === 0) {
    errors.push('Sell conditions are required and must not be empty')
  } else {
    rules.sellConditions.forEach((condition, index) => {
      validateSignalCondition(condition, `sellConditions[${index}]`, errors, warnings)
    })
  }

  // Validate hold conditions (optional)
  if (rules.holdConditions) {
    if (!Array.isArray(rules.holdConditions)) {
      errors.push('Hold conditions must be an array')
    } else {
      rules.holdConditions.forEach((condition, index) => {
        validateSignalCondition(condition, `holdConditions[${index}]`, errors, warnings)
      })
    }
  }
}

/**
 * Validates a signal condition
 */
function validateSignalCondition(
  condition: SignalCondition,
  context: string,
  errors: string[],
  warnings: string[]
): void {
  // Validate condition type
  if (!Object.values(SIGNAL_CONDITION_TYPES).includes(condition.type as any)) {
    errors.push(`Invalid condition type in ${context}: ${condition.type}`)
    return
  }

  // Validate parameters based on condition type
  switch (condition.type) {
    case SIGNAL_CONDITION_TYPES.THRESHOLD:
      validateThresholdCondition(condition, context, errors, warnings)
      break
    case SIGNAL_CONDITION_TYPES.CROSSOVER:
      validateCrossoverCondition(condition, context, errors, warnings)
      break
    case SIGNAL_CONDITION_TYPES.PATTERN:
      validatePatternCondition(condition, context, errors, warnings)
      break
    default:
      warnings.push(`Validation not implemented for condition type: ${condition.type}`)
  }
}

/**
 * Validates threshold condition parameters
 */
function validateThresholdCondition(
  condition: SignalCondition,
  context: string,
  errors: string[],
  warnings: string[]
): void {
  const { params } = condition

  if (params.threshold === undefined || typeof params.threshold !== 'number') {
    errors.push(`Threshold value is required in ${context}`)
  }

  if (params.operator && !Object.values(COMPARISON_OPERATORS).includes(params.operator as any)) {
    errors.push(`Invalid operator in ${context}: ${params.operator}`)
  }
}

/**
 * Validates crossover condition parameters
 */
function validateCrossoverCondition(
  condition: SignalCondition,
  context: string,
  errors: string[],
  warnings: string[]
): void {
  const { params } = condition

  if (!params.reference) {
    errors.push(`Reference value is required for crossover condition in ${context}`)
  }

  if (params.direction && !Object.values(CROSSOVER_DIRECTIONS).includes(params.direction as any)) {
    errors.push(`Invalid direction in ${context}: ${params.direction}`)
  }
}

/**
 * Validates pattern condition parameters
 */
function validatePatternCondition(
  condition: SignalCondition,
  context: string,
  errors: string[],
  warnings: string[]
): void {
  const { params } = condition

  if (params.lookback !== undefined) {
    if (
      typeof params.lookback !== 'number' ||
      params.lookback < VALIDATION_CONSTRAINTS.MIN_LOOKBACK_PERIOD ||
      params.lookback > VALIDATION_CONSTRAINTS.MAX_LOOKBACK_PERIOD
    ) {
      errors.push(`Invalid lookback period in ${context}`)
    }
  }
}

/**
 * Validates indicator weights
 */
function validateIndicatorWeights(
  weights: IndicatorWeight[],
  indicators: string[],
  errors: string[],
  warnings: string[]
): void {
  // Check if all indicators have weights
  indicators.forEach((indicator) => {
    const weight = weights.find((w) => w.indicator === indicator)
    if (!weight) {
      errors.push(`Missing weight for indicator: ${indicator}`)
    }
  })

  // Validate individual weights
  weights.forEach((weight, index) => {
    if (typeof weight.weight !== 'number') {
      errors.push(`Weight value must be a number at index ${index}`)
    } else if (
      weight.weight < VALIDATION_CONSTRAINTS.MIN_WEIGHT ||
      weight.weight > VALIDATION_CONSTRAINTS.MAX_WEIGHT
    ) {
      errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_WEIGHT + ` at index ${index}`)
    }
  })

  // Check if weights sum to 1.0
  const totalWeight = weights.reduce((sum, weight) => sum + (weight.weight || 0), 0)
  if (Math.abs(totalWeight - 1.0) > 0.001) {
    errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.WEIGHTS_SUM_INVALID)
  }
}

/**
 * Type guard for single indicator strategy
 */
function isSingleIndicatorStrategy(config: StrategyConfig): config is SingleIndicatorStrategyConfig {
  return 'indicator' in config && typeof config.indicator === 'string'
}

/**
 * Type guard for multi-indicator strategy
 */
function isMultiIndicatorStrategy(config: StrategyConfig): config is MultiIndicatorStrategyConfig {
  return 'indicators' in config && Array.isArray(config.indicators)
}

/**
 * Validates combination logic
 */
function isValidCombinationLogic(logic: unknown): logic is StrategyCombinationLogic {
  return typeof logic === 'string' && Object.values(COMBINATION_LOGIC).includes(logic as any)
}

/**
 * Validates trading signal
 */
export function isValidTradingSignal(signal: unknown): signal is TradingSignal {
  return typeof signal === 'string' && Object.values(TRADING_SIGNALS).includes(signal as any)
}

/**
 * Validates generated signal
 */
export function validateGeneratedSignal(signal: GeneratedSignal): StrategyValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate signal type
  if (!isValidTradingSignal(signal.signal)) {
    errors.push(`Invalid signal type: ${signal.signal}`)
  }

  // Validate confidence
  if (
    typeof signal.confidence !== 'number' ||
    signal.confidence < VALIDATION_CONSTRAINTS.MIN_CONFIDENCE ||
    signal.confidence > VALIDATION_CONSTRAINTS.MAX_CONFIDENCE
  ) {
    errors.push(SIGNAL_ENGINE_ERROR_MESSAGES.INVALID_CONFIDENCE)
  }

  // Validate timestamp
  if (typeof signal.timestamp !== 'number' || signal.timestamp <= 0) {
    errors.push('Invalid timestamp')
  }

  // Validate strategy name
  if (!signal.strategy || typeof signal.strategy !== 'string') {
    errors.push('Strategy name is required')
  }

  // Validate indicators array
  if (!Array.isArray(signal.indicators)) {
    errors.push('Indicators array is required')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Sanitizes error messages for safe logging
 */
export function sanitizeSignalError(error: unknown): string {
  if (error instanceof SignalValidationError) {
    return `Validation Error${error.field ? ` (${error.field})` : ''}: ${error.message}`
  }

  if (error instanceof Error) {
    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  return 'Unknown signal engine error'
}
