/**
 * Simple test runner for Signal Engine functionality
 */

async function testSignalEngine() {
  console.log('🚀 Testing Signal Engine Implementation...')
  console.log('='.repeat(60))

  try {
    // Test 1: Import and create SignalEngine
    console.log('\n📊 Test 1: SignalEngine Creation')
    const { SignalEngine } = await import('./src/main/core/SignalEngine.ts')
    const signalEngine = SignalEngine.getInstance()
    console.log('✅ SignalEngine instance created successfully')

    // Test 2: Create a simple RSI strategy
    console.log('\n🎯 Test 2: Strategy Creation')
    const strategy = signalEngine.createStrategy('rsi', {
      name: 'test_rsi_strategy',
      description: 'Test RSI strategy',
      period: 14,
      overboughtThreshold: 70,
      oversoldThreshold: 30,
      maxSignalHistory: 100
    })
    console.log(`✅ Strategy created: ${strategy.name}`)

    // Test 3: Process market data
    console.log('\n📈 Test 3: Market Data Processing')
    const testData = [
      { value: 100, timestamp: Date.now() },
      { value: 105, timestamp: Date.now() + 1000 },
      { value: 110, timestamp: Date.now() + 2000 },
      { value: 108, timestamp: Date.now() + 3000 },
      { value: 95, timestamp: Date.now() + 4000 }
    ]

    let signalCount = 0
    for (const dataPoint of testData) {
      const signals = signalEngine.processMarketData(dataPoint)
      signalCount += signals.length
      if (signals.length > 0) {
        console.log(
          `   Signal generated: ${signals[0].signal} (${(signals[0].confidence * 100).toFixed(1)}%)`
        )
      }
    }
    console.log(`✅ Processed ${testData.length} data points, generated ${signalCount} signals`)

    // Test 4: Performance metrics
    console.log('\n📊 Test 4: Performance Metrics')
    const metrics = signalEngine.getPerformanceMetrics()
    console.log(`✅ Performance metrics retrieved:`)
    console.log(`   - Total signals: ${metrics.totalSignalsGenerated}`)
    console.log(`   - Active strategies: ${signalEngine.getActiveStrategies().length}`)
    console.log(`   - Processing time: ${metrics.averageProcessingTime.toFixed(2)}ms`)

    // Test 5: Strategy removal
    console.log('\n🗑️ Test 5: Strategy Removal')
    const removed = signalEngine.removeStrategy('test_rsi_strategy')
    console.log(`✅ Strategy removed: ${removed}`)

    // Test 6: Test Bollinger Bands indicator
    console.log('\n📊 Test 6: Bollinger Bands Strategy')
    const bbStrategy = signalEngine.createStrategy('bollingerbands', {
      name: 'test_bb_strategy',
      description: 'Test Bollinger Bands strategy',
      period: 20,
      standardDeviations: 2,
      maxSignalHistory: 100
    })
    console.log(`✅ Bollinger Bands strategy created: ${bbStrategy.name}`)

    // Test 7: Multi-indicator strategy
    console.log('\n🔧 Test 7: Multi-Indicator Strategy')
    const multiStrategy = signalEngine.createStrategy(['rsi', 'sma'], {
      name: 'test_multi_strategy',
      description: 'Test multi-indicator strategy',
      rsi: { period: 14, overboughtThreshold: 70, oversoldThreshold: 30 },
      sma: { period: 20 },
      combinationLogic: 'AND',
      maxSignalHistory: 100
    })
    console.log(`✅ Multi-indicator strategy created: ${multiStrategy.name}`)

    console.log('\n🎉 All Signal Engine tests passed!')
    return true
  } catch (error) {
    console.error('\n❌ Signal Engine test failed:', error.message)
    console.error('Stack trace:', error.stack)
    return false
  }
}

async function testEventSystem() {
  console.log('\n🔄 Testing Event System...')
  console.log('-'.repeat(40))

  try {
    // Test event emitter
    const { SignalEngineEventEmitter } = await import(
      './src/main/events/SignalEngineEventEmitter.ts'
    )
    const eventEmitter = SignalEngineEventEmitter.getInstance()

    console.log('✅ Event emitter instance created')

    // Test event emission
    let eventReceived = false
    const cleanup = eventEmitter.addListener('signal-generated', (data) => {
      console.log(`✅ Event received: signal-generated`)
      eventReceived = true
    })

    // Emit test event
    eventEmitter.emitSignalEngineEvent('signal-generated', {
      signal: {
        signal: 'BUY',
        confidence: 0.8,
        timestamp: Date.now(),
        strategy: 'test',
        metadata: {}
      },
      timestamp: Date.now()
    })

    // Wait a bit for event processing
    await new Promise((resolve) => setTimeout(resolve, 100))

    if (eventReceived) {
      console.log('✅ Event system working correctly')
    } else {
      console.log('❌ Event not received')
    }

    cleanup()
    return eventReceived
  } catch (error) {
    console.error('❌ Event system test failed:', error.message)
    return false
  }
}

async function testConstants() {
  console.log('\n📋 Testing Constants Organization...')
  console.log('-'.repeat(40))

  try {
    // Test constants import
    const constants = await import('./src/shared/constants/index.ts')
    console.log('✅ Constants imported successfully')

    // Test specific constants
    if (constants.SIGNAL_ENGINE_CONSTANTS) {
      console.log('✅ Signal Engine constants available')
    }
    if (constants.UI_CONSTANTS) {
      console.log('✅ UI constants available')
    }
    if (constants.WEBSOCKET_CONSTANTS) {
      console.log('✅ WebSocket constants available')
    }

    return true
  } catch (error) {
    console.error('❌ Constants test failed:', error.message)
    return false
  }
}

async function testTypes() {
  console.log('\n🔧 Testing Types Organization...')
  console.log('-'.repeat(40))

  try {
    // Test types import
    const types = await import('./src/shared/types/index.ts')
    console.log('✅ Types imported successfully')

    // Test utility functions
    if (types.TypeUtils) {
      console.log('✅ Type utilities available')
    }
    if (types.TypeDefaults) {
      console.log('✅ Type defaults available')
    }

    return true
  } catch (error) {
    console.error('❌ Types test failed:', error.message)
    return false
  }
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Running Signal Engine Implementation Tests')
  console.log('='.repeat(80))

  const results = []

  results.push(await testSignalEngine())
  results.push(await testEventSystem())
  results.push(await testConstants())
  results.push(await testTypes())

  const passed = results.filter((r) => r).length
  const total = results.length

  console.log('\n📈 Test Summary')
  console.log('='.repeat(80))
  console.log(`Total Tests: ${total}`)
  console.log(`Passed: ${passed} ✅`)
  console.log(`Failed: ${total - passed} ❌`)
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`)

  if (passed === total) {
    console.log('\n🎉 All tests passed! Signal Engine implementation is working correctly.')
  } else {
    console.log('\n⚠️ Some tests failed. Please check the implementation.')
  }

  return passed === total
}

// Run the tests
runAllTests()
  .then((success) => {
    process.exit(success ? 0 : 1)
  })
  .catch((error) => {
    console.error('Test runner failed:', error)
    process.exit(1)
  })
