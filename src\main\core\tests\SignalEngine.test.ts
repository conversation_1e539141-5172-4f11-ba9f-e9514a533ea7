/**
 * SignalEngine Comprehensive Test Suite
 * Unit tests, integration tests, and end-to-end signal generation scenarios
 * Enhanced with comprehensive coverage for all indicators and strategy combinations
 */

import { SignalEngine } from '../SignalEngine'
import { StrategyFactory } from '../StrategyFactory'
import type {
  GeneratedSignal,
  Strategy,
  SignalEnginePerformanceMetrics,
  IndicatorDataPoint,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig,
  StrategyConfig
} from '../../../shared/types/signals'
import { createRSI, createSMA, createBollingerBands } from '../../indicators'
import { PersistenceManager } from '../../persistence/PersistenceManager'
import { BacktestEngine } from '../../backtesting/BacktestEngine'

/**
 * Test constants and configuration
 */
const TEST_CONFIG = {
  FLOATING_POINT_PRECISION: 0.001,
  PERFORMANCE_THRESHOLD_MS: 100,
  ASYNC_TEST_TIMEOUT_MS: 5000,
  LAR<PERSON>_DATASET_SIZE: 1000,
  SIGNAL_GENERATION_TIMEOUT: 2000,
  INTEGRATION_TEST_TIMEOUT: 10000,
  BACKTEST_DATA_SIZE: 500,
  PERSISTENCE_TEST_SIGNALS: 50
}

/**
 * Test data for signal generation
 */
const MARKET_DATA_TRENDING_UP = [
  { value: 100, timestamp: Date.now() },
  { value: 102, timestamp: Date.now() + 1000 },
  { value: 105, timestamp: Date.now() + 2000 },
  { value: 108, timestamp: Date.now() + 3000 },
  { value: 112, timestamp: Date.now() + 4000 },
  { value: 115, timestamp: Date.now() + 5000 },
  { value: 118, timestamp: Date.now() + 6000 },
  { value: 122, timestamp: Date.now() + 7000 },
  { value: 125, timestamp: Date.now() + 8000 },
  { value: 128, timestamp: Date.now() + 9000 }
]

const MARKET_DATA_TRENDING_DOWN = [
  { value: 128, timestamp: Date.now() },
  { value: 125, timestamp: Date.now() + 1000 },
  { value: 122, timestamp: Date.now() + 2000 },
  { value: 118, timestamp: Date.now() + 3000 },
  { value: 115, timestamp: Date.now() + 4000 },
  { value: 112, timestamp: Date.now() + 5000 },
  { value: 108, timestamp: Date.now() + 6000 },
  { value: 105, timestamp: Date.now() + 7000 },
  { value: 102, timestamp: Date.now() + 8000 },
  { value: 100, timestamp: Date.now() + 9000 }
]

const MARKET_DATA_VOLATILE = [
  { value: 100, timestamp: Date.now() },
  { value: 110, timestamp: Date.now() + 1000 },
  { value: 95, timestamp: Date.now() + 2000 },
  { value: 115, timestamp: Date.now() + 3000 },
  { value: 90, timestamp: Date.now() + 4000 },
  { value: 120, timestamp: Date.now() + 5000 },
  { value: 85, timestamp: Date.now() + 6000 },
  { value: 125, timestamp: Date.now() + 7000 },
  { value: 80, timestamp: Date.now() + 8000 },
  { value: 130, timestamp: Date.now() + 9000 }
]

/**
 * Utility functions for testing
 */
const TestUtils = {
  /**
   * Reset SignalEngine singleton for clean testing
   */
  resetSignalEngine(): void {
    SignalEngine.resetInstance()
  },

  /**
   * Create test market data with specified pattern
   */
  createMarketData(
    pattern: 'up' | 'down' | 'volatile' | 'sideways',
    count: number = 20
  ): IndicatorDataPoint[] {
    const baseTime = Date.now()
    const data: IndicatorDataPoint[] = []

    for (let i = 0; i < count; i++) {
      let value: number

      switch (pattern) {
        case 'up':
          value = 100 + i * 2 + Math.random() * 2
          break
        case 'down':
          value = 120 - i * 2 + Math.random() * 2
          break
        case 'volatile':
          value = 100 + Math.sin(i * 0.5) * 20 + Math.random() * 10
          break
        case 'sideways':
          value = 100 + Math.random() * 4 - 2
          break
        default:
          value = 100
      }

      data.push({
        value,
        timestamp: baseTime + i * 1000
      })
    }

    return data
  },

  /**
   * Wait for async operations
   */
  async wait(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  },

  /**
   * Check if two numbers are approximately equal
   */
  areNumbersEqual(
    a: number,
    b: number,
    precision: number = TEST_CONFIG.FLOATING_POINT_PRECISION
  ): boolean {
    return Math.abs(a - b) < precision
  }
}

/**
 * Unit Tests for SignalEngine Core Functionality
 */
export class SignalEngineUnitTests {
  /**
   * Test SignalEngine singleton pattern
   */
  static testSingletonPattern(): boolean {
    console.log('🔧 Testing SignalEngine singleton pattern...')

    try {
      TestUtils.resetSignalEngine()

      const instance1 = SignalEngine.getInstance()
      const instance2 = SignalEngine.getInstance()

      if (instance1 !== instance2) {
        console.error('❌ Singleton pattern failed - different instances returned')
        return false
      }

      console.log('✅ Singleton pattern test passed')
      return true
    } catch (error) {
      console.error('❌ Singleton pattern test error:', error)
      return false
    }
  }

  /**
   * Test strategy creation and management
   */
  static testStrategyCreation(): boolean {
    console.log('🔧 Testing strategy creation and management...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Test single indicator strategy creation
      const rsiStrategy = signalEngine.createStrategy('rsi', {
        name: 'test_rsi_strategy',
        period: 14,
        overbought: 70,
        oversold: 30
      })

      if (!rsiStrategy || rsiStrategy.name !== 'test_rsi_strategy') {
        console.error('❌ RSI strategy creation failed')
        return false
      }

      // Test multi-indicator strategy creation
      const multiStrategy = signalEngine.createStrategy(['rsi', 'sma'], {
        name: 'test_multi_strategy',
        rsi: { period: 14, overbought: 70, oversold: 30 },
        sma: { period: 20 },
        combinationLogic: 'AND'
      })

      if (!multiStrategy || multiStrategy.name !== 'test_multi_strategy') {
        console.error('❌ Multi-indicator strategy creation failed')
        return false
      }

      // Test strategy retrieval
      const activeStrategies = signalEngine.getActiveStrategies()
      if (activeStrategies.length !== 2) {
        console.error(`❌ Expected 2 active strategies, got ${activeStrategies.length}`)
        return false
      }

      console.log('✅ Strategy creation and management test passed')
      return true
    } catch (error) {
      console.error('❌ Strategy creation test error:', error)
      return false
    }
  }

  /**
   * Test signal generation with single indicator
   */
  static testSingleIndicatorSignalGeneration(): boolean {
    console.log('🔧 Testing single indicator signal generation...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create RSI strategy with oversold condition
      const strategy = signalEngine.createStrategy('rsi', {
        name: 'oversold_rsi',
        period: 5, // Short period for quick testing
        oversold: 30
      })

      // Generate market data that should trigger oversold condition
      const marketData = TestUtils.createMarketData('down', 10)

      let signalGenerated = false
      for (const dataPoint of marketData) {
        const signals = signalEngine.processMarketData(dataPoint)
        if (signals.length > 0) {
          signalGenerated = true
          const signal = signals[0]

          if (signal.signal !== 'BUY' && signal.signal !== 'SELL') {
            console.error('❌ Invalid signal type generated:', signal.signal)
            return false
          }

          if (signal.confidence < 0 || signal.confidence > 1) {
            console.error('❌ Invalid confidence level:', signal.confidence)
            return false
          }

          break
        }
      }

      if (!signalGenerated) {
        console.warn('⚠️  No signals generated - this may be expected depending on market data')
      }

      console.log('✅ Single indicator signal generation test passed')
      return true
    } catch (error) {
      console.error('❌ Single indicator signal generation test error:', error)
      return false
    }
  }

  /**
   * Test performance metrics tracking
   */
  static testPerformanceMetrics(): boolean {
    console.log('🔧 Testing performance metrics tracking...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create a strategy
      signalEngine.createStrategy('rsi', {
        name: 'performance_test',
        period: 10
      })

      // Process some data
      const marketData = TestUtils.createMarketData('volatile', 20)
      for (const dataPoint of marketData) {
        signalEngine.processMarketData(dataPoint)
      }

      // Check performance metrics
      const metrics = signalEngine.getPerformanceMetrics()

      if (!metrics) {
        console.error('❌ Performance metrics not available')
        return false
      }

      if (
        typeof metrics.totalSignalsGenerated !== 'number' ||
        typeof metrics.averageProcessingTime !== 'number' ||
        typeof metrics.totalProcessingTime !== 'number'
      ) {
        console.error('❌ Invalid performance metrics structure')
        return false
      }

      console.log(`   Total signals: ${metrics.totalSignalsGenerated}`)
      console.log(`   Avg processing time: ${metrics.averageProcessingTime.toFixed(2)}ms`)
      console.log('✅ Performance metrics test passed')
      return true
    } catch (error) {
      console.error('❌ Performance metrics test error:', error)
      return false
    }
  }

  /**
   * Test error handling scenarios
   */
  static testErrorHandling(): boolean {
    console.log('🔧 Testing error handling scenarios...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Test invalid indicator name
      try {
        signalEngine.createStrategy('invalid_indicator', {})
        console.error('❌ Should have thrown error for invalid indicator')
        return false
      } catch (error) {
        // Expected error
      }

      // Test invalid configuration
      try {
        signalEngine.createStrategy('rsi', { period: -1 })
        console.error('❌ Should have thrown error for invalid period')
        return false
      } catch (error) {
        // Expected error
      }

      // Test invalid market data
      try {
        signalEngine.processMarketData({ value: NaN, timestamp: Date.now() })
        // This might not throw an error depending on validation settings
      } catch (error) {
        // Expected in some cases
      }

      console.log('✅ Error handling test passed')
      return true
    } catch (error) {
      console.error('❌ Error handling test error:', error)
      return false
    }
  }
}

/**
 * Integration Tests for SignalEngine with Multiple Indicators
 */
export class SignalEngineIntegrationTests {
  /**
   * Test multi-indicator strategy with AND logic
   */
  static testMultiIndicatorANDLogic(): boolean {
    console.log('🔗 Testing multi-indicator strategy with AND logic...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create strategy that requires both RSI oversold AND price below SMA
      const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
        name: 'rsi_sma_and_strategy',
        rsi: { period: 5, oversold: 30 },
        sma: { period: 5 },
        combinationLogic: 'AND'
      })

      // Generate market data that should satisfy both conditions
      const marketData = TestUtils.createMarketData('down', 15)

      let andSignalGenerated = false
      for (const dataPoint of marketData) {
        const signals = signalEngine.processMarketData(dataPoint)
        if (signals.length > 0) {
          andSignalGenerated = true
          console.log(
            `   AND signal generated: ${signals[0].signal} (confidence: ${(signals[0].confidence * 100).toFixed(1)}%)`
          )
          break
        }
      }

      console.log('✅ Multi-indicator AND logic test passed')
      return true
    } catch (error) {
      console.error('❌ Multi-indicator AND logic test error:', error)
      return false
    }
  }

  /**
   * Test multi-indicator strategy with OR logic
   */
  static testMultiIndicatorORLogic(): boolean {
    console.log('🔗 Testing multi-indicator strategy with OR logic...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create strategy that triggers on either RSI overbought OR price above SMA
      const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
        name: 'rsi_sma_or_strategy',
        rsi: { period: 5, overbought: 70 },
        sma: { period: 5 },
        combinationLogic: 'OR'
      })

      // Generate market data that should satisfy at least one condition
      const marketData = TestUtils.createMarketData('up', 15)

      let orSignalGenerated = false
      for (const dataPoint of marketData) {
        const signals = signalEngine.processMarketData(dataPoint)
        if (signals.length > 0) {
          orSignalGenerated = true
          console.log(
            `   OR signal generated: ${signals[0].signal} (confidence: ${(signals[0].confidence * 100).toFixed(1)}%)`
          )
          break
        }
      }

      console.log('✅ Multi-indicator OR logic test passed')
      return true
    } catch (error) {
      console.error('❌ Multi-indicator OR logic test error:', error)
      return false
    }
  }

  /**
   * Test weighted strategy combination
   */
  static testWeightedStrategy(): boolean {
    console.log('🔗 Testing weighted strategy combination...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create weighted strategy with different indicator weights
      const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
        name: 'weighted_strategy',
        rsi: { period: 10 },
        sma: { period: 10 },
        combinationLogic: 'WEIGHTED',
        weights: [
          { indicator: 'rsi', weight: 0.7 },
          { indicator: 'sma', weight: 0.3 }
        ]
      })

      // Process market data
      const marketData = TestUtils.createMarketData('volatile', 20)

      let weightedSignalGenerated = false
      for (const dataPoint of marketData) {
        const signals = signalEngine.processMarketData(dataPoint)
        if (signals.length > 0) {
          weightedSignalGenerated = true
          const signal = signals[0]

          // Weighted signals should have confidence influenced by weights
          if (signal.confidence < 0 || signal.confidence > 1) {
            console.error('❌ Invalid weighted confidence level:', signal.confidence)
            return false
          }

          console.log(
            `   Weighted signal: ${signal.signal} (confidence: ${(signal.confidence * 100).toFixed(1)}%)`
          )
          break
        }
      }

      console.log('✅ Weighted strategy test passed')
      return true
    } catch (error) {
      console.error('❌ Weighted strategy test error:', error)
      return false
    }
  }

  /**
   * Test Bollinger Bands integration
   */
  static testBollingerBandsIntegration(): boolean {
    console.log('🔗 Testing Bollinger Bands integration...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create Bollinger Bands strategy
      const strategy = signalEngine.createStrategy('bollingerbands', {
        name: 'bb_strategy',
        period: 10,
        standardDeviations: 2
      })

      // Generate market data with high volatility to trigger band signals
      const marketData = TestUtils.createMarketData('volatile', 25)

      let bbSignalGenerated = false
      for (const dataPoint of marketData) {
        const signals = signalEngine.processMarketData(dataPoint)
        if (signals.length > 0) {
          bbSignalGenerated = true
          console.log(
            `   Bollinger Bands signal: ${signals[0].signal} (confidence: ${(signals[0].confidence * 100).toFixed(1)}%)`
          )
          break
        }
      }

      console.log('✅ Bollinger Bands integration test passed')
      return true
    } catch (error) {
      console.error('❌ Bollinger Bands integration test error:', error)
      return false
    }
  }
}

/**
 * End-to-End Signal Generation Scenarios
 */
export class SignalEngineE2ETests {
  /**
   * Test complete trading scenario - Bull Market
   */
  static async testBullMarketScenario(): Promise<boolean> {
    console.log('🎯 Testing bull market scenario...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create comprehensive strategy for bull market
      const strategy = signalEngine.createStrategy(['rsi', 'sma', 'bollingerbands'], {
        name: 'bull_market_strategy',
        rsi: { period: 14, overbought: 70, oversold: 30 },
        sma: { period: 20 },
        bollingerbands: { period: 20, standardDeviations: 2 },
        combinationLogic: 'MAJORITY'
      })

      // Simulate bull market data
      const bullMarketData = TestUtils.createMarketData('up', 50)

      const signals: GeneratedSignal[] = []
      for (const dataPoint of bullMarketData) {
        const generatedSignals = signalEngine.processMarketData(dataPoint)
        signals.push(...generatedSignals)

        // Add small delay to simulate real-time processing
        await TestUtils.wait(10)
      }

      console.log(`   Generated ${signals.length} signals in bull market scenario`)

      // Analyze signal distribution
      const buySignals = signals.filter((s) => s.signal === 'BUY').length
      const sellSignals = signals.filter((s) => s.signal === 'SELL').length
      const holdSignals = signals.filter((s) => s.signal === 'HOLD').length

      console.log(`   BUY: ${buySignals}, SELL: ${sellSignals}, HOLD: ${holdSignals}`)

      // In a bull market, we might expect more BUY signals
      console.log('✅ Bull market scenario test passed')
      return true
    } catch (error) {
      console.error('❌ Bull market scenario test error:', error)
      return false
    }
  }

  /**
   * Test complete trading scenario - Bear Market
   */
  static async testBearMarketScenario(): Promise<boolean> {
    console.log('🎯 Testing bear market scenario...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create comprehensive strategy for bear market
      const strategy = signalEngine.createStrategy(['rsi', 'sma'], {
        name: 'bear_market_strategy',
        rsi: { period: 14, overbought: 70, oversold: 30 },
        sma: { period: 20 },
        combinationLogic: 'OR'
      })

      // Simulate bear market data
      const bearMarketData = TestUtils.createMarketData('down', 50)

      const signals: GeneratedSignal[] = []
      for (const dataPoint of bearMarketData) {
        const generatedSignals = signalEngine.processMarketData(dataPoint)
        signals.push(...generatedSignals)

        await TestUtils.wait(10)
      }

      console.log(`   Generated ${signals.length} signals in bear market scenario`)

      // Analyze signal distribution
      const buySignals = signals.filter((s) => s.signal === 'BUY').length
      const sellSignals = signals.filter((s) => s.signal === 'SELL').length

      console.log(`   BUY: ${buySignals}, SELL: ${sellSignals}`)

      console.log('✅ Bear market scenario test passed')
      return true
    } catch (error) {
      console.error('❌ Bear market scenario test error:', error)
      return false
    }
  }

  /**
   * Test performance under high-frequency data
   */
  static testHighFrequencyPerformance(): boolean {
    console.log('🎯 Testing high-frequency performance...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create multiple strategies
      signalEngine.createStrategy('rsi', { name: 'hf_rsi', period: 10 })
      signalEngine.createStrategy('sma', { name: 'hf_sma', period: 15 })
      signalEngine.createStrategy('bollingerbands', { name: 'hf_bb', period: 20 })

      // Generate large dataset
      const largeDataset = TestUtils.createMarketData('volatile', TEST_CONFIG.LARGE_DATASET_SIZE)

      const startTime = performance.now()

      let totalSignals = 0
      for (const dataPoint of largeDataset) {
        const signals = signalEngine.processMarketData(dataPoint)
        totalSignals += signals.length
      }

      const endTime = performance.now()
      const processingTime = endTime - startTime

      console.log(
        `   Processed ${TEST_CONFIG.LARGE_DATASET_SIZE} data points in ${processingTime.toFixed(2)}ms`
      )
      console.log(`   Generated ${totalSignals} total signals`)
      console.log(
        `   Average time per data point: ${(processingTime / TEST_CONFIG.LARGE_DATASET_SIZE).toFixed(4)}ms`
      )

      if (
        processingTime >
        (TEST_CONFIG.PERFORMANCE_THRESHOLD_MS * TEST_CONFIG.LARGE_DATASET_SIZE) / 100
      ) {
        console.warn(`⚠️  Performance slower than expected: ${processingTime.toFixed(2)}ms`)
        return false
      }

      console.log('✅ High-frequency performance test passed')
      return true
    } catch (error) {
      console.error('❌ High-frequency performance test error:', error)
      return false
    }
  }
}

/**
 * Test Suite Configuration
 */
interface TestSuite {
  name: string
  testFunction: () => boolean | Promise<boolean>
  category: 'unit' | 'integration' | 'e2e'
}

/**
 * All test suites to run
 */
const ALL_TEST_SUITES: readonly TestSuite[] = [
  // Unit Tests
  {
    name: 'Singleton Pattern',
    testFunction: SignalEngineUnitTests.testSingletonPattern,
    category: 'unit'
  },
  {
    name: 'Strategy Creation',
    testFunction: SignalEngineUnitTests.testStrategyCreation,
    category: 'unit'
  },
  {
    name: 'Single Indicator Signal Generation',
    testFunction: SignalEngineUnitTests.testSingleIndicatorSignalGeneration,
    category: 'unit'
  },
  {
    name: 'Performance Metrics',
    testFunction: SignalEngineUnitTests.testPerformanceMetrics,
    category: 'unit'
  },
  {
    name: 'Error Handling',
    testFunction: SignalEngineUnitTests.testErrorHandling,
    category: 'unit'
  },

  // Integration Tests
  {
    name: 'Multi-Indicator AND Logic',
    testFunction: SignalEngineIntegrationTests.testMultiIndicatorANDLogic,
    category: 'integration'
  },
  {
    name: 'Multi-Indicator OR Logic',
    testFunction: SignalEngineIntegrationTests.testMultiIndicatorORLogic,
    category: 'integration'
  },
  {
    name: 'Weighted Strategy',
    testFunction: SignalEngineIntegrationTests.testWeightedStrategy,
    category: 'integration'
  },
  {
    name: 'Bollinger Bands Integration',
    testFunction: SignalEngineIntegrationTests.testBollingerBandsIntegration,
    category: 'integration'
  },

  // End-to-End Tests
  {
    name: 'Bull Market Scenario',
    testFunction: SignalEngineE2ETests.testBullMarketScenario,
    category: 'e2e'
  },
  {
    name: 'Bear Market Scenario',
    testFunction: SignalEngineE2ETests.testBearMarketScenario,
    category: 'e2e'
  },
  {
    name: 'High-Frequency Performance',
    testFunction: SignalEngineE2ETests.testHighFrequencyPerformance,
    category: 'e2e'
  }
] as const

/**
 * Main test runner for SignalEngine comprehensive test suite
 */
export async function runSignalEngineTests(
  category?: 'unit' | 'integration' | 'e2e' | 'all'
): Promise<boolean> {
  console.log('🚀 Starting SignalEngine Comprehensive Test Suite...\n')

  const testCategory = category || 'all'
  const testsToRun =
    testCategory === 'all'
      ? ALL_TEST_SUITES
      : ALL_TEST_SUITES.filter((test) => test.category === testCategory)

  console.log(`Running ${testsToRun.length} tests in category: ${testCategory}\n`)

  let passedTests = 0
  const totalTests = testsToRun.length
  const results: { name: string; category: string; passed: boolean; error?: string }[] = []

  for (const testSuite of testsToRun) {
    try {
      console.log(`\n📋 Running: ${testSuite.name} (${testSuite.category})`)
      console.log('─'.repeat(60))

      const result = await testSuite.testFunction()

      if (result) {
        passedTests++
        console.log(`✅ ${testSuite.name} - PASSED`)
        results.push({ name: testSuite.name, category: testSuite.category, passed: true })
      } else {
        console.log(`❌ ${testSuite.name} - FAILED`)
        results.push({ name: testSuite.name, category: testSuite.category, passed: false })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error(`💥 ${testSuite.name} - ERROR:`, errorMessage)
      results.push({
        name: testSuite.name,
        category: testSuite.category,
        passed: false,
        error: errorMessage
      })
    }
  }

  // Generate comprehensive summary
  console.log('\n' + '='.repeat(80))
  console.log('📊 SIGNAL ENGINE TEST SUITE SUMMARY')
  console.log('='.repeat(80))

  // Overall statistics
  console.log(`Total Tests: ${totalTests}`)
  console.log(`Passed: ${passedTests}`)
  console.log(`Failed: ${totalTests - passedTests}`)
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

  // Category breakdown
  const categories = ['unit', 'integration', 'e2e']
  for (const cat of categories) {
    const categoryResults = results.filter((r) => r.category === cat)
    if (categoryResults.length > 0) {
      const categoryPassed = categoryResults.filter((r) => r.passed).length
      console.log(`${cat.toUpperCase()}: ${categoryPassed}/${categoryResults.length} passed`)
    }
  }

  // Failed tests details
  const failedTests = results.filter((r) => !r.passed)
  if (failedTests.length > 0) {
    console.log('\n❌ Failed Tests:')
    failedTests.forEach((test) => {
      console.log(`   - ${test.name} (${test.category})${test.error ? ': ' + test.error : ''}`)
    })
  }

  const allPassed = passedTests === totalTests
  if (allPassed) {
    console.log('\n🎉 All tests passed! SignalEngine implementation is working correctly.')
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.')
  }

  return allPassed
}

/**
 * Quick test function for development
 */
export const quickSignalEngineTest = async (): Promise<boolean> => {
  console.log('⚡ Running quick SignalEngine test...')

  try {
    TestUtils.resetSignalEngine()
    const signalEngine = SignalEngine.getInstance()

    // Create a simple strategy
    const strategy = signalEngine.createStrategy('rsi', {
      name: 'quick_test',
      period: 5
    })

    // Process some test data
    const testData = TestUtils.createMarketData('volatile', 10)
    let signalCount = 0

    for (const dataPoint of testData) {
      const signals = signalEngine.processMarketData(dataPoint)
      signalCount += signals.length
    }

    console.log(`✅ Quick test passed - Generated ${signalCount} signals`)
    return true
  } catch (error) {
    console.error('❌ Quick test error:', error)
    return false
  }
}

/**
 * Enhanced Integration Tests for Signal Engine
 * Comprehensive testing of all indicator combinations and real-world scenarios
 */
export class EnhancedIntegrationTests {
  /**
   * Test multi-indicator strategy with all supported indicators
   */
  static async testMultiIndicatorStrategy(): Promise<boolean> {
    console.log('🔄 Testing multi-indicator strategy integration...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()

      // Create a comprehensive multi-indicator strategy
      const multiConfig: MultiIndicatorStrategyConfig = {
        name: 'comprehensive_multi_strategy',
        description: 'RSI + SMA + Bollinger Bands combined strategy',
        indicators: ['rsi', 'sma', 'bollingerbands'],
        indicatorConfigs: {
          rsi: { period: 14, overboughtThreshold: 70, oversoldThreshold: 30 },
          sma: { period: 20 },
          bollingerbands: { period: 20, standardDeviations: 2 }
        },
        signalRules: {
          buyConditions: [{ type: 'threshold', params: { threshold: 30, operator: 'lt' } }],
          sellConditions: [{ type: 'threshold', params: { threshold: 70, operator: 'gt' } }]
        },
        combinationLogic: 'AND',
        weights: [
          { indicator: 'rsi', weight: 0.4 },
          { indicator: 'sma', weight: 0.3 },
          { indicator: 'bollingerbands', weight: 0.3 }
        ]
      }

      const strategy = signalEngine.createStrategy(['rsi', 'sma', 'bollingerbands'], multiConfig)
      if (!strategy) {
        console.error('❌ Failed to create multi-indicator strategy')
        return false
      }

      // Test with various market conditions
      const testScenarios = [
        { name: 'Trending Up', data: TestUtils.createMarketData('up', 30) },
        { name: 'Trending Down', data: TestUtils.createMarketData('down', 30) },
        { name: 'Volatile', data: TestUtils.createMarketData('volatile', 30) },
        { name: 'Sideways', data: TestUtils.createMarketData('sideways', 30) }
      ]

      let totalSignals = 0
      for (const scenario of testScenarios) {
        console.log(`   Testing scenario: ${scenario.name}`)

        for (const dataPoint of scenario.data) {
          const signals = signalEngine.processMarketData(dataPoint)
          totalSignals += signals.length

          // Validate signal quality
          for (const signal of signals) {
            if (signal.confidence < 0 || signal.confidence > 1) {
              console.error(`❌ Invalid confidence level: ${signal.confidence}`)
              return false
            }

            if (!['BUY', 'SELL', 'HOLD'].includes(signal.signal)) {
              console.error(`❌ Invalid signal type: ${signal.signal}`)
              return false
            }
          }
        }
      }

      console.log(`   Generated ${totalSignals} signals across all scenarios`)

      // Test performance metrics
      const metrics = signalEngine.getPerformanceMetrics()
      if (!metrics || metrics.totalSignalsGenerated < 0) {
        console.error('❌ Invalid performance metrics')
        return false
      }

      console.log('✅ Multi-indicator strategy integration test passed')
      return true
    } catch (error) {
      console.error('❌ Multi-indicator strategy test failed:', error)
      return false
    }
  }

  /**
   * Test signal persistence integration
   */
  static async testSignalPersistence(): Promise<boolean> {
    console.log('🔄 Testing signal persistence integration...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()
      const persistenceManager = PersistenceManager.getInstance()

      // Create a simple strategy for testing
      const strategy = signalEngine.createStrategy('rsi', {
        name: 'persistence_test_strategy',
        period: 14
      })

      if (!strategy) {
        console.error('❌ Failed to create strategy for persistence test')
        return false
      }

      // Generate signals and test persistence
      const testData = TestUtils.createMarketData('volatile', TEST_CONFIG.PERSISTENCE_TEST_SIGNALS)
      const generatedSignals: GeneratedSignal[] = []

      for (const dataPoint of testData) {
        const signals = signalEngine.processMarketData(dataPoint)
        generatedSignals.push(...signals)

        // Save each signal to persistence
        for (const signal of signals) {
          const result = await persistenceManager.saveSignal(signal, {
            testRun: true,
            scenario: 'integration_test'
          })

          if (!result.success) {
            console.error(`❌ Failed to save signal: ${result.error}`)
            return false
          }
        }
      }

      // Test signal retrieval
      if (generatedSignals.length > 0) {
        const retrievedSignals = await persistenceManager.getSignals({
          strategy: 'persistence_test_strategy',
          limit: generatedSignals.length
        })

        if (!retrievedSignals.success || !retrievedSignals.data) {
          console.error('❌ Failed to retrieve saved signals')
          return false
        }

        if (retrievedSignals.data.length !== generatedSignals.length) {
          console.error(
            `❌ Signal count mismatch: expected ${generatedSignals.length}, got ${retrievedSignals.data.length}`
          )
          return false
        }
      }

      console.log(`✅ Signal persistence test passed (${generatedSignals.length} signals)`)
      return true
    } catch (error) {
      console.error('❌ Signal persistence test failed:', error)
      return false
    }
  }

  /**
   * Test backtesting integration
   */
  static async testBacktestingIntegration(): Promise<boolean> {
    console.log('🔄 Testing backtesting integration...')

    try {
      TestUtils.resetSignalEngine()
      const signalEngine = SignalEngine.getInstance()
      const backtestEngine = BacktestEngine.getInstance()

      // Create a strategy for backtesting
      const strategy = signalEngine.createStrategy('rsi', {
        name: 'backtest_strategy',
        period: 14,
        overboughtThreshold: 70,
        oversoldThreshold: 30
      })

      if (!strategy) {
        console.error('❌ Failed to create strategy for backtest')
        return false
      }

      // Generate historical data for backtesting
      const historicalData = TestUtils.createMarketData('volatile', TEST_CONFIG.BACKTEST_DATA_SIZE)

      // Run backtest
      const backtestResult = await backtestEngine.runBacktest(strategy, historicalData, {
        initialCapital: 10000,
        execution: {
          executionDelay: 0,
          slippage: 0.001,
          commission: 0.1
        },
        riskManagement: {
          maxPositionSize: 0.1,
          stopLoss: 0.05,
          takeProfit: 0.1
        }
      })

      // Validate backtest results
      if (!backtestResult || !backtestResult.metrics) {
        console.error('❌ Invalid backtest results')
        return false
      }

      const { metrics } = backtestResult
      if (
        typeof metrics.totalReturn !== 'number' ||
        typeof metrics.sharpeRatio !== 'number' ||
        typeof metrics.maxDrawdown !== 'number'
      ) {
        console.error('❌ Invalid backtest metrics')
        return false
      }

      console.log(`   Backtest Results:`)
      console.log(`   - Total Return: ${metrics.totalReturn.toFixed(2)}%`)
      console.log(`   - Sharpe Ratio: ${metrics.sharpeRatio.toFixed(2)}`)
      console.log(`   - Max Drawdown: ${metrics.maxDrawdown.toFixed(2)}%`)
      console.log(`   - Total Trades: ${backtestResult.trades.length}`)

      console.log('✅ Backtesting integration test passed')
      return true
    } catch (error) {
      console.error('❌ Backtesting integration test failed:', error)
      return false
    }
  }
}

/**
 * Enhanced comprehensive test runner
 */
export const runEnhancedSignalEngineTests = async (): Promise<boolean> => {
  console.log('🚀 Running Enhanced Signal Engine Test Suite...')
  console.log('='.repeat(60))

  const testResults: { name: string; passed: boolean; duration: number }[] = []

  // Enhanced Integration Tests
  console.log('\n📊 Enhanced Integration Tests')
  console.log('-'.repeat(40))

  const enhancedTests = [
    { name: 'Multi-Indicator Strategy', test: EnhancedIntegrationTests.testMultiIndicatorStrategy },
    { name: 'Signal Persistence', test: EnhancedIntegrationTests.testSignalPersistence },
    { name: 'Backtesting Integration', test: EnhancedIntegrationTests.testBacktestingIntegration }
  ]

  for (const { name, test } of enhancedTests) {
    const startTime = Date.now()
    try {
      const passed = await test()
      const duration = Date.now() - startTime
      testResults.push({ name, passed, duration })

      if (passed) {
        console.log(`✅ ${name} - ${duration}ms`)
      } else {
        console.log(`❌ ${name} - ${duration}ms`)
      }
    } catch (error) {
      const duration = Date.now() - startTime
      testResults.push({ name, passed: false, duration })
      console.log(`❌ ${name} - ${duration}ms (Error: ${error})`)
    }
  }

  // Run original test suites
  console.log('\n🔧 Original Test Suites')
  console.log('-'.repeat(40))

  const originalTests = [
    { name: 'Unit Tests', test: () => SignalEngineUnitTests.runTests() },
    { name: 'Integration Tests', test: () => SignalEngineIntegrationTests.runTests() },
    { name: 'E2E Tests', test: () => SignalEngineE2ETests.runTests() }
  ]

  for (const { name, test } of originalTests) {
    const startTime = Date.now()
    try {
      const passed = await test()
      const duration = Date.now() - startTime
      testResults.push({ name, passed, duration })

      if (passed) {
        console.log(`✅ ${name} - ${duration}ms`)
      } else {
        console.log(`❌ ${name} - ${duration}ms`)
      }
    } catch (error) {
      const duration = Date.now() - startTime
      testResults.push({ name, passed: false, duration })
      console.log(`❌ ${name} - ${duration}ms (Error: ${error})`)
    }
  }

  // Summary
  console.log('\n📈 Test Summary')
  console.log('='.repeat(60))

  const totalTests = testResults.length
  const passedTests = testResults.filter((r) => r.passed).length
  const failedTests = totalTests - passedTests
  const totalDuration = testResults.reduce((sum, r) => sum + r.duration, 0)

  console.log(`Total Tests: ${totalTests}`)
  console.log(`Passed: ${passedTests} ✅`)
  console.log(`Failed: ${failedTests} ❌`)
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
  console.log(`Total Duration: ${totalDuration}ms`)
  console.log(`Average Duration: ${(totalDuration / totalTests).toFixed(1)}ms`)

  if (failedTests > 0) {
    console.log('\n❌ Failed Tests:')
    testResults
      .filter((r) => !r.passed)
      .forEach((r) => {
        console.log(`   - ${r.name} (${r.duration}ms)`)
      })
  }

  const allPassed = failedTests === 0
  console.log(`\n${allPassed ? '🎉 All tests passed!' : '⚠️  Some tests failed'}`)

  return allPassed
}

/**
 * Public API for the test module
 */
export const SignalEngineTestSuite = {
  // Main test runners
  runTests: runSignalEngineTests,
  runEnhancedTests: runEnhancedSignalEngineTests,
  quickTest: quickSignalEngineTest,

  // Test classes for selective testing
  UnitTests: SignalEngineUnitTests,
  IntegrationTests: SignalEngineIntegrationTests,
  E2ETests: SignalEngineE2ETests,
  EnhancedIntegrationTests,

  // Utility functions
  TestUtils,

  // Test constants
  TEST_CONFIG
} as const

// Export default for convenience
export default SignalEngineTestSuite
