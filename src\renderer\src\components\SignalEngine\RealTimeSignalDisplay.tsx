/**
 * Real-time Signal Display Component
 * Live feed showing incoming signals with visual indicators, colors, icons, progress bars, cooldown timers, and confidence levels
 */

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { SignalEngine } from '../../../../main/core/SignalEngine'
import type { GeneratedSignal } from '../../../../shared/types/signals'

/**
 * Signal display component props
 */
interface RealTimeSignalDisplayProps {
  /** Custom CSS classes */
  className?: string
  /** Maximum number of signals to display */
  maxSignals?: number
  /** Auto-scroll to new signals */
  autoScroll?: boolean
  /** Show signal details */
  showDetails?: boolean
  /** Signal filter by strategy */
  strategyFilter?: string
  /** Signal filter by type */
  typeFilter?: 'BUY' | 'SELL' | 'HOLD' | 'ALL'
}

/**
 * Enhanced signal with display metadata
 */
interface EnhancedSignal extends GeneratedSignal {
  id: string
  isNew: boolean
  fadeOut: boolean
}

/**
 * Signal statistics
 */
interface SignalStats {
  total: number
  buy: number
  sell: number
  hold: number
  avgConfidence: number
  lastSignalTime: number
}

/**
 * Real-time Signal Display Component
 */
export const RealTimeSignalDisplay: React.FC<RealTimeSignalDisplayProps> = ({
  className = '',
  maxSignals = 50,
  autoScroll = true,
  showDetails = true,
  strategyFilter,
  typeFilter = 'ALL'
}) => {
  // State management
  const [signals, setSignals] = useState<EnhancedSignal[]>([])
  const [stats, setStats] = useState<SignalStats>({
    total: 0,
    buy: 0,
    sell: 0,
    hold: 0,
    avgConfidence: 0,
    lastSignalTime: 0
  })
  const [isPaused, setIsPaused] = useState(false)
  const [nextSignalCooldown, setNextSignalCooldown] = useState(0)

  // Refs
  const containerRef = useRef<HTMLDivElement>(null)
  const cooldownTimerRef = useRef<NodeJS.Timeout>()

  /**
   * Get signal type color classes
   */
  const getSignalColorClasses = (signal: string): string => {
    switch (signal) {
      case 'BUY':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'SELL':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HOLD':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  /**
   * Get signal icon
   */
  const getSignalIcon = (signal: string): string => {
    switch (signal) {
      case 'BUY':
        return '📈'
      case 'SELL':
        return '📉'
      case 'HOLD':
        return '⏸️'
      default:
        return '❓'
    }
  }

  /**
   * Get confidence level color
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    if (confidence >= 0.4) return 'text-orange-600'
    return 'text-red-600'
  }

  /**
   * Get confidence level description
   */
  const getConfidenceDescription = (confidence: number): string => {
    if (confidence >= 0.8) return 'Very High'
    if (confidence >= 0.6) return 'High'
    if (confidence >= 0.4) return 'Medium'
    if (confidence >= 0.2) return 'Low'
    return 'Very Low'
  }

  /**
   * Filter signals based on current filters
   */
  const filterSignals = useCallback(
    (signalList: EnhancedSignal[]): EnhancedSignal[] => {
      return signalList.filter((signal) => {
        // Strategy filter
        if (strategyFilter && signal.strategy !== strategyFilter) {
          return false
        }

        // Type filter
        if (typeFilter !== 'ALL' && signal.signal !== typeFilter) {
          return false
        }

        return true
      })
    },
    [strategyFilter, typeFilter]
  )

  /**
   * Update signal statistics
   */
  const updateStats = useCallback(
    (signalList: EnhancedSignal[]) => {
      const filteredSignals = filterSignals(signalList)

      const newStats: SignalStats = {
        total: filteredSignals.length,
        buy: filteredSignals.filter((s) => s.signal === 'BUY').length,
        sell: filteredSignals.filter((s) => s.signal === 'SELL').length,
        hold: filteredSignals.filter((s) => s.signal === 'HOLD').length,
        avgConfidence:
          filteredSignals.length > 0
            ? filteredSignals.reduce((sum, s) => sum + s.confidence, 0) / filteredSignals.length
            : 0,
        lastSignalTime:
          filteredSignals.length > 0 ? Math.max(...filteredSignals.map((s) => s.timestamp)) : 0
      }

      setStats(newStats)
    },
    [filterSignals]
  )

  /**
   * Handle new signal
   */
  const handleNewSignal = useCallback(
    (signal: GeneratedSignal) => {
      if (isPaused) return

      const enhancedSignal: EnhancedSignal = {
        ...signal,
        id: `${signal.timestamp}-${Math.random().toString(36).substr(2, 9)}`,
        isNew: true,
        fadeOut: false
      }

      setSignals((prev) => {
        const newSignals = [enhancedSignal, ...prev].slice(0, maxSignals)

        // Mark signal as not new after animation
        setTimeout(() => {
          setSignals((current) =>
            current.map((s) => (s.id === enhancedSignal.id ? { ...s, isNew: false } : s))
          )
        }, 1000)

        return newSignals
      })

      // Start cooldown timer
      setNextSignalCooldown(5) // 5 second cooldown
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current)
      }

      cooldownTimerRef.current = setInterval(() => {
        setNextSignalCooldown((prev) => {
          if (prev <= 1) {
            if (cooldownTimerRef.current) {
              clearInterval(cooldownTimerRef.current)
            }
            return 0
          }
          return prev - 1
        })
      }, 1000)
    },
    [isPaused, maxSignals]
  )

  /**
   * Auto-scroll to top when new signal arrives
   */
  useEffect(() => {
    if (autoScroll && containerRef.current) {
      containerRef.current.scrollTop = 0
    }
  }, [signals, autoScroll])

  /**
   * Update statistics when signals change
   */
  useEffect(() => {
    updateStats(signals)
  }, [signals, updateStats])

  /**
   * Set up signal listener
   */
  useEffect(() => {
    const signalEngine = SignalEngine.getInstance()
    signalEngine.on('signal-generated', handleNewSignal)

    return () => {
      signalEngine.off('signal-generated', handleNewSignal)
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current)
      }
    }
  }, [handleNewSignal])

  /**
   * Clear all signals
   */
  const clearSignals = useCallback(() => {
    setSignals([])
  }, [])

  /**
   * Toggle pause/resume
   */
  const togglePause = useCallback(() => {
    setIsPaused((prev) => !prev)
  }, [])

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString()
  }

  /**
   * Format time ago
   */
  const formatTimeAgo = (timestamp: number): string => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000)
    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ago`
  }

  const filteredSignals = filterSignals(signals)

  return (
    <div
      className={`real-time-signal-display bg-white rounded-lg border border-gray-200 ${className}`}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <span className="mr-2">📡</span>
            Real-time Signals
          </h3>
          <div className="flex items-center space-x-3">
            {/* Cooldown Timer */}
            {nextSignalCooldown > 0 && (
              <div className="flex items-center space-x-2">
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${((5 - nextSignalCooldown) / 5) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-500">{nextSignalCooldown}s</span>
              </div>
            )}

            {/* Controls */}
            <button
              onClick={togglePause}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                isPaused
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
              }`}
            >
              {isPaused ? '▶️ Resume' : '⏸️ Pause'}
            </button>
            <button
              onClick={clearSignals}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-sm font-medium hover:bg-red-200 transition-colors"
            >
              🗑️ Clear
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-xs text-gray-500">Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.buy}</div>
            <div className="text-xs text-gray-500">Buy</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.sell}</div>
            <div className="text-xs text-gray-500">Sell</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.hold}</div>
            <div className="text-xs text-gray-500">Hold</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${getConfidenceColor(stats.avgConfidence)}`}>
              {(stats.avgConfidence * 100).toFixed(0)}%
            </div>
            <div className="text-xs text-gray-500">Avg Confidence</div>
          </div>
        </div>
      </div>

      {/* Signal List */}
      <div ref={containerRef} className="p-6 max-h-96 overflow-y-auto">
        {filteredSignals.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📡</div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              {isPaused ? 'Signal Feed Paused' : 'Waiting for Signals'}
            </h4>
            <p className="text-gray-500">
              {isPaused
                ? 'Click Resume to continue receiving signals'
                : 'Real-time signals will appear here when generated'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredSignals.map((signal) => (
              <div
                key={signal.id}
                className={`signal-item p-4 rounded-lg border transition-all duration-500 ${
                  signal.isNew
                    ? 'transform scale-105 shadow-lg border-green-300 bg-green-50'
                    : getSignalColorClasses(signal.signal)
                } ${signal.fadeOut ? 'opacity-50' : ''}`}
              >
                <div className="flex items-center justify-between">
                  {/* Signal Info */}
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{getSignalIcon(signal.signal)}</div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-bold text-lg">{signal.signal}</span>
                        <span className="text-sm text-gray-600">•</span>
                        <span className="font-medium text-gray-900">{signal.strategy}</span>
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-sm text-gray-500">
                          {formatTimestamp(signal.timestamp)}
                        </span>
                        <span className="text-xs text-gray-400">
                          {formatTimeAgo(signal.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Confidence Display */}
                  <div className="text-right">
                    <div className={`text-lg font-bold ${getConfidenceColor(signal.confidence)}`}>
                      {(signal.confidence * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-500">
                      {getConfidenceDescription(signal.confidence)}
                    </div>

                    {/* Confidence Progress Bar */}
                    <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          signal.confidence >= 0.8
                            ? 'bg-green-500'
                            : signal.confidence >= 0.6
                              ? 'bg-yellow-500'
                              : signal.confidence >= 0.4
                                ? 'bg-orange-500'
                                : 'bg-red-500'
                        }`}
                        style={{ width: `${signal.confidence * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {/* Signal Details */}
                {showDetails && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Timestamp:</span>
                        <span className="ml-2 font-mono">{signal.timestamp}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Strategy:</span>
                        <span className="ml-2">{signal.strategy}</span>
                      </div>
                      {signal.metadata && (
                        <div className="col-span-2">
                          <span className="text-gray-500">Metadata:</span>
                          <pre className="ml-2 text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                            {JSON.stringify(signal.metadata, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* New Signal Indicator */}
                {signal.isNew && (
                  <div className="absolute top-2 right-2">
                    <div className="bg-green-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">
                      NEW
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div>
            Showing {filteredSignals.length} of {signals.length} signals
            {strategyFilter && (
              <span className="ml-2">
                • Filtered by: <span className="font-medium">{strategyFilter}</span>
              </span>
            )}
            {typeFilter !== 'ALL' && (
              <span className="ml-2">
                • Type: <span className="font-medium">{typeFilter}</span>
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {stats.lastSignalTime > 0 && (
              <span>Last signal: {formatTimeAgo(stats.lastSignalTime)}</span>
            )}
            <div
              className={`w-2 h-2 rounded-full ${
                isPaused ? 'bg-yellow-500' : 'bg-green-500 animate-pulse'
              }`}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RealTimeSignalDisplay
