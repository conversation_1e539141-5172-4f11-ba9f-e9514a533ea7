/**
 * Centralized type exports for the Signal Engine system
 * This file provides organized access to all types, interfaces, constants, and utilities
 */

// Re-export all original types for backward compatibility
export * from './signals'
export * from './indicators'
export * from './backtesting'
export * from './persistence'
export * from './trading'
export * from './broker'

// Re-export organized types
export * from './interfaces'
export * from './utilities'

// Re-export constants
export * from '../constants'

// Namespace exports for better organization
export { SignalEngineInterfaces } from './interfaces'
export * as SignalEngineUtilities from './utilities'
export { ALL_CONSTANTS as SignalEngineConstants } from '../constants'

// Common type aliases for convenience
export type {
  // Core types
  Strategy,
  GeneratedSignal,
  IndicatorDataPoint,
  
  // Configuration types
  StrategyConfig,
  IndicatorConfig,
  BacktestConfig,
  
  // Result types
  BacktestResult,
  PersistenceResult,
  
  // Utility types
  Result,
  AsyncResult,
  Nullable,
  Optional,
  Maybe
} from './interfaces'

export type {
  // Branded types
  StrategyId,
  SignalId,
  IndicatorId,
  Timestamp,
  Percentage,
  Currency,
  
  // Function types
  EventHandler,
  AsyncEventHandler,
  Validator,
  AsyncValidator,
  
  // State types
  State,
  Store,
  Cache
} from './utilities'

export type {
  // Constant types
  SignalType,
  StrategyType,
  CombinationLogic,
  IndicatorType,
  ConnectionState,
  ErrorCode,
  ErrorSeverity
} from '../constants'

/**
 * Convenience re-exports for commonly used types
 * These provide quick access to the most frequently used types
 */

// Signal Engine core types
export type {
  Strategy as IStrategy,
  GeneratedSignal as IGeneratedSignal,
  StrategyConfig as IStrategyConfig,
  SignalEnginePerformanceMetrics as IPerformanceMetrics
} from './signals'

// Indicator types
export type {
  IndicatorDataPoint as IIndicatorDataPoint,
  IndicatorConfig as IIndicatorConfig,
  RSIConfig as IRSIConfig,
  SMAConfig as ISMAConfig,
  BollingerBandsConfig as IBollingerBandsConfig
} from './indicators'

// Backtesting types
export type {
  BacktestConfig as IBacktestConfig,
  BacktestResult as IBacktestResult,
  BacktestMetrics as IBacktestMetrics,
  TradeRecord as ITradeRecord
} from './backtesting'

// Persistence types
export type {
  PersistenceConfig as IPersistenceConfig,
  PersistenceResult as IPersistenceResult,
  SignalQuery as ISignalQuery
} from './persistence'

// Trading types
export type {
  Position as IPosition,
  Order as IOrder,
  Portfolio as IPortfolio,
  MarketData as IMarketData
} from './trading'

// Broker types
export type {
  BrokerConfig as IBrokerConfig,
  BrokerConnection as IBrokerConnection,
  BrokerMetrics as IBrokerMetrics
} from './broker'

/**
 * Type utility functions for runtime type checking
 */
export const TypeUtils = {
  // Signal type guards
  isStrategy: (value: unknown): value is Strategy => {
    return typeof value === 'object' && value !== null && 'name' in value && 'indicators' in value
  },
  
  isGeneratedSignal: (value: unknown): value is GeneratedSignal => {
    return typeof value === 'object' && value !== null && 
           'signal' in value && 'confidence' in value && 'timestamp' in value
  },
  
  isIndicatorDataPoint: (value: unknown): value is IndicatorDataPoint => {
    return typeof value === 'object' && value !== null && 
           'value' in value && 'timestamp' in value
  },
  
  // Configuration type guards
  isStrategyConfig: (value: unknown): value is StrategyConfig => {
    return typeof value === 'object' && value !== null && 'name' in value
  },
  
  isBacktestConfig: (value: unknown): value is BacktestConfig => {
    return typeof value === 'object' && value !== null && 
           'initialCapital' in value && 'execution' in value
  },
  
  // Result type guards
  isResult: <T>(value: unknown): value is Result<T> => {
    return typeof value === 'object' && value !== null && 'success' in value
  },
  
  isSuccessResult: <T>(value: Result<T>): value is { success: true; data: T } => {
    return value.success === true
  },
  
  isErrorResult: <T>(value: Result<T>): value is { success: false; error: any } => {
    return value.success === false
  }
} as const

/**
 * Type assertion functions for development and debugging
 */
export const TypeAssertions = {
  assertStrategy: (value: unknown): asserts value is Strategy => {
    if (!TypeUtils.isStrategy(value)) {
      throw new Error('Expected Strategy object')
    }
  },
  
  assertGeneratedSignal: (value: unknown): asserts value is GeneratedSignal => {
    if (!TypeUtils.isGeneratedSignal(value)) {
      throw new Error('Expected GeneratedSignal object')
    }
  },
  
  assertIndicatorDataPoint: (value: unknown): asserts value is IndicatorDataPoint => {
    if (!TypeUtils.isIndicatorDataPoint(value)) {
      throw new Error('Expected IndicatorDataPoint object')
    }
  },
  
  assertResult: <T>(value: unknown): asserts value is Result<T> => {
    if (!TypeUtils.isResult(value)) {
      throw new Error('Expected Result object')
    }
  }
} as const

/**
 * Default values for common types
 */
export const TypeDefaults = {
  strategy: (): Partial<Strategy> => ({
    name: '',
    description: '',
    indicators: [],
    isReady: false,
    createdAt: Date.now()
  }),
  
  generatedSignal: (): Partial<GeneratedSignal> => ({
    signal: 'HOLD',
    confidence: 0,
    timestamp: Date.now(),
    strategy: '',
    metadata: {}
  }),
  
  indicatorDataPoint: (): Partial<IndicatorDataPoint> => ({
    value: 0,
    timestamp: Date.now()
  }),
  
  backtestConfig: (): Partial<BacktestConfig> => ({
    initialCapital: 10000,
    execution: {
      executionDelay: 0,
      slippage: 0.001,
      commission: 0.1
    },
    riskManagement: {
      maxPositionSize: 0.1,
      stopLoss: 0.05,
      takeProfit: 0.1
    }
  }),
  
  persistenceConfig: (): Partial<PersistenceConfig> => ({
    location: './data',
    autoSave: true,
    compression: false,
    encryption: false
  })
} as const

/**
 * Type conversion utilities
 */
export const TypeConverters = {
  // Convert timestamp to Date
  timestampToDate: (timestamp: Timestamp): Date => new Date(timestamp),
  
  // Convert Date to timestamp
  dateToTimestamp: (date: Date): Timestamp => date.getTime() as Timestamp,
  
  // Convert percentage to decimal
  percentageToDecimal: (percentage: Percentage): number => percentage / 100,
  
  // Convert decimal to percentage
  decimalToPercentage: (decimal: number): Percentage => (decimal * 100) as Percentage,
  
  // Convert currency to string with formatting
  currencyToString: (currency: Currency, symbol: string = '$'): string => {
    return `${symbol}${currency.toFixed(2)}`
  },
  
  // Parse currency from string
  stringToCurrency: (value: string): Currency => {
    const parsed = parseFloat(value.replace(/[^0-9.-]/g, ''))
    return (isNaN(parsed) ? 0 : parsed) as Currency
  }
} as const
