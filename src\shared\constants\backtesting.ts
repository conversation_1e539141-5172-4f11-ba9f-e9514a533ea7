/**
 * Backtesting Module Constants
 * Default configurations, validation constraints, and performance settings
 */

import type {
  BacktestConfig,
  RiskManagementConfig,
  ExecutionConfig,
  DefaultBacktestConfig,
  BacktestConstants
} from '../types/backtesting'

/**
 * Default backtesting configuration values
 */
export const DEFAULT_BACKTEST_CONFIG: BacktestConfig = {
  initialCapital: 100000, // $100,000
  commission: 0.001, // 0.1% per trade
  slippage: 0.0005, // 0.05% slippage
  maxPositionSize: 0.1, // 10% of capital per position
  riskManagement: {
    stopLoss: 0.02, // 2% stop loss
    takeProfit: 0.06, // 6% take profit (3:1 risk/reward)
    maxDrawdown: 0.15, // 15% maximum drawdown
    positionSizing: 'percentage',
    riskPerTrade: 0.01 // 1% risk per trade
  },
  execution: {
    executionDelay: 100, // 100ms execution delay
    marketImpact: 'linear',
    fillProbability: 0.98, // 98% fill probability
    allowPartialFills: true
  }
} as const

/**
 * Default risk management configuration
 */
export const DEFAULT_RISK_MANAGEMENT: RiskManagementConfig = {
  stopLoss: 0.02, // 2%
  takeProfit: 0.06, // 6%
  maxDrawdown: 0.15, // 15%
  positionSizing: 'percentage',
  riskPerTrade: 0.01 // 1%
} as const

/**
 * Default execution configuration
 */
export const DEFAULT_EXECUTION_CONFIG: ExecutionConfig = {
  executionDelay: 100, // 100ms
  marketImpact: 'linear',
  fillProbability: 0.98, // 98%
  allowPartialFills: true
} as const

/**
 * Validation constraints for backtesting parameters
 */
export const BACKTEST_VALIDATION_CONSTRAINTS = {
  initialCapital: {
    min: 1000,
    max: 10000000,
    default: 100000
  },
  commission: {
    min: 0,
    max: 0.01, // 1% max commission
    default: 0.001
  },
  slippage: {
    min: 0,
    max: 0.005, // 0.5% max slippage
    default: 0.0005
  },
  maxPositionSize: {
    min: 0.01, // 1% minimum
    max: 1.0, // 100% maximum
    default: 0.1
  },
  stopLoss: {
    min: 0.001, // 0.1% minimum
    max: 0.5, // 50% maximum
    default: 0.02
  },
  takeProfit: {
    min: 0.001, // 0.1% minimum
    max: 2.0, // 200% maximum
    default: 0.06
  },
  maxDrawdown: {
    min: 0.01, // 1% minimum
    max: 0.9, // 90% maximum
    default: 0.15
  },
  riskPerTrade: {
    min: 0.001, // 0.1% minimum
    max: 0.1, // 10% maximum
    default: 0.01
  },
  executionDelay: {
    min: 0,
    max: 10000, // 10 seconds max
    default: 100
  },
  fillProbability: {
    min: 0.1, // 10% minimum
    max: 1.0, // 100% maximum
    default: 0.98
  }
} as const

/**
 * Performance calculation constants
 */
export const PERFORMANCE_CONSTANTS = {
  TRADING_DAYS_PER_YEAR: 252,
  HOURS_PER_TRADING_DAY: 24, // For crypto markets
  MINUTES_PER_HOUR: 60,
  SECONDS_PER_MINUTE: 60,
  MILLISECONDS_PER_SECOND: 1000,
  RISK_FREE_RATE: 0.02, // 2% annual risk-free rate
  MIN_TRADES_FOR_STATISTICS: 10,
  SHARPE_RATIO_PERIODS: 252, // Annualized
  VOLATILITY_PERIODS: 252, // Annualized
  VAR_CONFIDENCE_LEVEL: 0.95, // 95% VaR
  EXPECTED_SHORTFALL_CONFIDENCE: 0.95 // 95% ES
} as const

/**
 * Position sizing methods and their parameters
 */
export const POSITION_SIZING_METHODS = {
  fixed: {
    name: 'Fixed Size',
    description: 'Fixed position size regardless of volatility or risk',
    defaultSize: 0.1 // 10% of capital
  },
  percentage: {
    name: 'Percentage of Capital',
    description: 'Position size as percentage of total capital',
    defaultSize: 0.1 // 10% of capital
  },
  kelly: {
    name: 'Kelly Criterion',
    description: 'Optimal position size based on win rate and average win/loss',
    maxSize: 0.25, // Maximum 25% of capital
    lookbackPeriod: 50 // Number of trades to look back
  },
  volatility: {
    name: 'Volatility-Based',
    description: 'Position size inversely proportional to volatility',
    targetVolatility: 0.15, // 15% target portfolio volatility
    lookbackPeriod: 20 // Days to calculate volatility
  }
} as const

/**
 * Market impact models
 */
export const MARKET_IMPACT_MODELS = {
  none: {
    name: 'No Impact',
    description: 'No market impact assumed',
    calculate: () => 0
  },
  linear: {
    name: 'Linear Impact',
    description: 'Linear relationship between order size and impact',
    impactFactor: 0.0001 // 0.01% impact per 1% of volume
  },
  square_root: {
    name: 'Square Root Impact',
    description: 'Square root relationship (more realistic for large orders)',
    impactFactor: 0.0005 // Base impact factor
  }
} as const

/**
 * Backtesting error messages
 */
export const BACKTEST_ERROR_MESSAGES = {
  INVALID_INITIAL_CAPITAL: 'Initial capital must be a positive number',
  INVALID_COMMISSION: 'Commission must be between 0 and 1%',
  INVALID_SLIPPAGE: 'Slippage must be between 0 and 0.5%',
  INVALID_POSITION_SIZE: 'Position size must be between 1% and 100%',
  INVALID_STOP_LOSS: 'Stop loss must be between 0.1% and 50%',
  INVALID_TAKE_PROFIT: 'Take profit must be between 0.1% and 200%',
  INVALID_RISK_PER_TRADE: 'Risk per trade must be between 0.1% and 10%',
  INSUFFICIENT_DATA: 'Insufficient historical data for backtesting',
  INVALID_STRATEGY: 'Strategy must be provided and ready',
  EXECUTION_ERROR: 'Error occurred during backtest execution',
  CALCULATION_ERROR: 'Error occurred during performance calculation'
} as const

/**
 * Backtesting event types and their descriptions
 */
export const BACKTEST_EVENTS = {
  'backtest-started': 'Backtesting process has started',
  'backtest-completed': 'Backtesting process has completed',
  'trade-opened': 'A new trade position has been opened',
  'trade-closed': 'An existing trade position has been closed',
  'portfolio-updated': 'Portfolio value has been updated',
  'risk-limit-hit': 'A risk management limit has been triggered',
  'error-occurred': 'An error occurred during backtesting'
} as const

/**
 * Performance metric categories for reporting
 */
export const METRIC_CATEGORIES = {
  returns: {
    name: 'Returns',
    metrics: ['totalReturn', 'annualizedReturn', 'monthlyReturns']
  },
  risk: {
    name: 'Risk Metrics',
    metrics: ['maxDrawdown', 'volatility', 'valueAtRisk', 'expectedShortfall']
  },
  ratios: {
    name: 'Risk-Adjusted Returns',
    metrics: ['sharpeRatio', 'sortinoRatio', 'calmarRatio', 'informationRatio']
  },
  trading: {
    name: 'Trading Statistics',
    metrics: ['totalTrades', 'winRate', 'profitFactor', 'averageTradeDuration']
  },
  benchmark: {
    name: 'Benchmark Comparison',
    metrics: ['alpha', 'beta', 'informationRatio']
  }
} as const

/**
 * Default configuration values for quick access
 */
export const BACKTEST_DEFAULTS: DefaultBacktestConfig = {
  INITIAL_CAPITAL: 100000,
  COMMISSION: 0.001,
  SLIPPAGE: 0.0005,
  MAX_POSITION_SIZE: 0.1,
  RISK_PER_TRADE: 0.01,
  EXECUTION_DELAY: 100,
  FILL_PROBABILITY: 0.98
} as const

/**
 * Complete backtesting constants object
 */
export const BACKTEST_CONSTANTS: BacktestConstants = {
  DEFAULT_CONFIG: BACKTEST_DEFAULTS,
  TRADING_DAYS_PER_YEAR: PERFORMANCE_CONSTANTS.TRADING_DAYS_PER_YEAR,
  HOURS_PER_TRADING_DAY: PERFORMANCE_CONSTANTS.HOURS_PER_TRADING_DAY,
  RISK_FREE_RATE: PERFORMANCE_CONSTANTS.RISK_FREE_RATE,
  MIN_TRADES_FOR_STATISTICS: PERFORMANCE_CONSTANTS.MIN_TRADES_FOR_STATISTICS,
  PERFORMANCE_CALCULATION: {
    SHARPE_RATIO_PERIODS: PERFORMANCE_CONSTANTS.SHARPE_RATIO_PERIODS,
    VOLATILITY_PERIODS: PERFORMANCE_CONSTANTS.VOLATILITY_PERIODS,
    VAR_CONFIDENCE_LEVEL: PERFORMANCE_CONSTANTS.VAR_CONFIDENCE_LEVEL
  }
} as const

/**
 * Export all constants for external use
 */
export {
  DEFAULT_BACKTEST_CONFIG,
  DEFAULT_RISK_MANAGEMENT,
  DEFAULT_EXECUTION_CONFIG,
  BACKTEST_VALIDATION_CONSTRAINTS,
  PERFORMANCE_CONSTANTS,
  POSITION_SIZING_METHODS,
  MARKET_IMPACT_MODELS,
  BACKTEST_ERROR_MESSAGES,
  BACKTEST_EVENTS,
  METRIC_CATEGORIES,
  BACKTEST_DEFAULTS,
  BACKTEST_CONSTANTS
}

/**
 * Default export for convenience
 */
export default {
  DEFAULT_CONFIG: DEFAULT_BACKTEST_CONFIG,
  VALIDATION: BACKTEST_VALIDATION_CONSTRAINTS,
  PERFORMANCE: PERFORMANCE_CONSTANTS,
  POSITION_SIZING: POSITION_SIZING_METHODS,
  MARKET_IMPACT: MARKET_IMPACT_MODELS,
  ERRORS: BACKTEST_ERROR_MESSAGES,
  EVENTS: BACKTEST_EVENTS,
  METRICS: METRIC_CATEGORIES,
  CONSTANTS: BACKTEST_CONSTANTS
}
