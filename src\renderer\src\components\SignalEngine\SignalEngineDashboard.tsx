/**
 * Signal Engine Dashboard Component
 * Main dashboard showing active strategies, real-time signals, and performance metrics
 */

import React, { useState, useEffect, useCallback } from 'react'
import { SignalEngine } from '../../../../main/core/SignalEngine'
import { useTradingContext } from '../../contexts/TradingContext'
import type { 
  GeneratedSignal, 
  Strategy, 
  SignalEnginePerformanceMetrics 
} from '../../../../shared/types/signals'

/**
 * Dashboard component props
 */
interface SignalEngineDashboardProps {
  /** Custom CSS classes */
  className?: string
  /** Dashboard refresh interval in milliseconds */
  refreshInterval?: number
  /** Maximum number of recent signals to display */
  maxRecentSignals?: number
}

/**
 * Dashboard state interface
 */
interface DashboardState {
  activeStrategies: Strategy[]
  recentSignals: GeneratedSignal[]
  performanceMetrics: SignalEnginePerformanceMetrics | null
  isLoading: boolean
  error: string | null
  lastUpdate: number
}

/**
 * Signal Engine Dashboard Component
 */
export const SignalEngineDashboard: React.FC<SignalEngineDashboardProps> = ({
  className = '',
  refreshInterval = 5000,
  maxRecentSignals = 10
}) => {
  // State management
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    activeStrategies: [],
    recentSignals: [],
    performanceMetrics: null,
    isLoading: true,
    error: null,
    lastUpdate: Date.now()
  })

  // Trading context
  const { isConnected, connectionStatus } = useTradingContext()

  /**
   * Update dashboard data
   */
  const updateDashboard = useCallback(async () => {
    try {
      setDashboardState(prev => ({ ...prev, isLoading: true, error: null }))

      const signalEngine = SignalEngine.getInstance()
      
      // Get active strategies
      const activeStrategies = signalEngine.getActiveStrategies()
      
      // Get recent signals
      const recentSignals = signalEngine.getRecentSignals(maxRecentSignals)
      
      // Get performance metrics
      const performanceMetrics = signalEngine.getPerformanceMetrics()

      setDashboardState({
        activeStrategies,
        recentSignals,
        performanceMetrics,
        isLoading: false,
        error: null,
        lastUpdate: Date.now()
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
    }
  }, [maxRecentSignals])

  /**
   * Handle real-time signal updates
   */
  const handleSignalUpdate = useCallback((signal: GeneratedSignal) => {
    setDashboardState(prev => ({
      ...prev,
      recentSignals: [signal, ...prev.recentSignals.slice(0, maxRecentSignals - 1)],
      lastUpdate: Date.now()
    }))
  }, [maxRecentSignals])

  // Initialize dashboard and set up real-time updates
  useEffect(() => {
    updateDashboard()

    // Set up refresh interval
    const interval = setInterval(updateDashboard, refreshInterval)

    // Set up real-time signal listener
    const signalEngine = SignalEngine.getInstance()
    signalEngine.on('signal-generated', handleSignalUpdate)

    return () => {
      clearInterval(interval)
      signalEngine.off('signal-generated', handleSignalUpdate)
    }
  }, [updateDashboard, refreshInterval, handleSignalUpdate])

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString()
  }

  /**
   * Get signal type color
   */
  const getSignalColor = (signal: string): string => {
    switch (signal) {
      case 'BUY':
        return 'text-green-600 bg-green-100'
      case 'SELL':
        return 'text-red-600 bg-red-100'
      case 'HOLD':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  /**
   * Get confidence level color
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  /**
   * Render loading state
   */
  if (dashboardState.isLoading && dashboardState.activeStrategies.length === 0) {
    return (
      <div className={`signal-engine-dashboard ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading Signal Engine Dashboard...</span>
        </div>
      </div>
    )
  }

  /**
   * Render error state
   */
  if (dashboardState.error) {
    return (
      <div className={`signal-engine-dashboard ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 text-xl mr-2">⚠️</span>
            <div>
              <h3 className="text-red-800 font-medium">Dashboard Error</h3>
              <p className="text-red-600 text-sm mt-1">{dashboardState.error}</p>
            </div>
          </div>
          <button
            onClick={updateDashboard}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`signal-engine-dashboard space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h2 className="text-2xl font-bold text-gray-900">📊 Signal Engine Dashboard</h2>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {connectionStatus}
          </div>
        </div>
        <div className="text-sm text-gray-500">
          Last updated: {formatTimestamp(dashboardState.lastUpdate)}
        </div>
      </div>

      {/* Performance Metrics Overview */}
      {dashboardState.performanceMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Signals</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardState.performanceMetrics.totalSignalsGenerated}
                </p>
              </div>
              <div className="text-2xl">🎯</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Processing</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardState.performanceMetrics.averageProcessingTime.toFixed(1)}ms
                </p>
              </div>
              <div className="text-2xl">⚡</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Strategies</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardState.activeStrategies.length}
                </p>
              </div>
              <div className="text-2xl">⚙️</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Processing</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardState.performanceMetrics.totalProcessingTime.toFixed(0)}ms
                </p>
              </div>
              <div className="text-2xl">📈</div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Strategies */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <span className="mr-2">⚙️</span>
              Active Strategies
            </h3>
          </div>
          <div className="p-6">
            {dashboardState.activeStrategies.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">🔍</div>
                <p className="text-gray-500">No active strategies</p>
                <p className="text-sm text-gray-400 mt-1">Create a strategy to start generating signals</p>
              </div>
            ) : (
              <div className="space-y-3">
                {dashboardState.activeStrategies.map((strategy, index) => (
                  <div
                    key={strategy.id || index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{strategy.name}</h4>
                      <p className="text-sm text-gray-600">{strategy.description || 'No description'}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`px-2 py-1 rounded text-xs font-medium ${
                        strategy.isReady ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {strategy.isReady ? 'Ready' : 'Initializing'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recent Signals */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <span className="mr-2">🎯</span>
              Recent Signals
            </h3>
          </div>
          <div className="p-6">
            {dashboardState.recentSignals.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">📡</div>
                <p className="text-gray-500">No recent signals</p>
                <p className="text-sm text-gray-400 mt-1">Signals will appear here when generated</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {dashboardState.recentSignals.map((signal, index) => (
                  <div
                    key={`${signal.timestamp}-${index}`}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs font-bold ${getSignalColor(signal.signal)}`}>
                          {signal.signal}
                        </span>
                        <span className="text-sm font-medium text-gray-900">{signal.strategy}</span>
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(signal.timestamp)}
                        </span>
                        <span className={`text-xs font-medium ${getConfidenceColor(signal.confidence)}`}>
                          {(signal.confidence * 100).toFixed(1)}% confidence
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignalEngineDashboard
