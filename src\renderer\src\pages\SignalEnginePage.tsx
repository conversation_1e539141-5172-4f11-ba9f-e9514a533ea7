/**
 * Signal Engine Page
 * Main page integrating all Signal Engine components with comprehensive layout and state management
 */

import React, { useState, useCallback } from 'react'
import { SignalEngineDashboard } from '../components/SignalEngine/SignalEngineDashboard'
import { StrategyBuilder } from '../components/SignalEngine/StrategyBuilder'
import { RealTimeSignalDisplay } from '../components/SignalEngine/RealTimeSignalDisplay'
import { PerformanceAnalyticsPanel } from '../components/SignalEngine/PerformanceAnalyticsPanel'
import type { StrategyConfig } from '../../../shared/types/signals'

/**
 * Page layout modes
 */
type LayoutMode = 'dashboard' | 'builder' | 'signals' | 'analytics'

/**
 * Signal Engine Page Component
 */
export const SignalEnginePage: React.FC = () => {
  // State management
  const [activeLayout, setActiveLayout] = useState<LayoutMode>('dashboard')
  const [showStrategyBuilder, setShowStrategyBuilder] = useState(false)
  const [selectedStrategy, setSelectedStrategy] = useState<string>()
  const [signalFilter, setSignalFilter] = useState<'ALL' | 'BUY' | 'SELL' | 'HOLD'>('ALL')

  /**
   * Handle strategy creation
   */
  const handleStrategyCreated = useCallback((strategy: StrategyConfig) => {
    console.log('Strategy created:', strategy)
    setShowStrategyBuilder(false)
    // In a real implementation, you might want to refresh the dashboard
  }, [])

  /**
   * Handle layout change
   */
  const handleLayoutChange = useCallback((layout: LayoutMode) => {
    setActiveLayout(layout)
    if (layout === 'builder') {
      setShowStrategyBuilder(true)
    }
  }, [])

  /**
   * Navigation items
   */
  const navigationItems = [
    {
      id: 'dashboard' as LayoutMode,
      label: 'Dashboard',
      icon: '📊',
      description: 'Overview and metrics'
    },
    {
      id: 'builder' as LayoutMode,
      label: 'Strategy Builder',
      icon: '⚙️',
      description: 'Create strategies'
    },
    {
      id: 'signals' as LayoutMode,
      label: 'Live Signals',
      icon: '📡',
      description: 'Real-time feed'
    },
    {
      id: 'analytics' as LayoutMode,
      label: 'Analytics',
      icon: '📈',
      description: 'Performance analysis'
    }
  ]

  return (
    <div className="signal-engine-page min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Page Title */}
            <div className="flex items-center space-x-3">
              <h1 className="text-2xl font-bold text-gray-900">🎯 Signal Engine</h1>
              <div className="hidden sm:block text-sm text-gray-500">
                Advanced trading signal generation and analysis
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowStrategyBuilder(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <span>⚙️</span>
                <span className="hidden sm:inline">New Strategy</span>
              </button>
              
              {/* Signal Filter */}
              <select
                value={signalFilter}
                onChange={(e) => setSignalFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="ALL">All Signals</option>
                <option value="BUY">BUY Only</option>
                <option value="SELL">SELL Only</option>
                <option value="HOLD">HOLD Only</option>
              </select>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex space-x-1 -mb-px">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleLayoutChange(item.id)}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activeLayout === item.id
                    ? 'border-green-500 text-green-600 bg-green-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <span>{item.icon}</span>
                  <span className="hidden sm:inline">{item.label}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Dashboard Layout */}
        {activeLayout === 'dashboard' && (
          <div className="space-y-6">
            <SignalEngineDashboard />
            
            {/* Quick Overview Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <RealTimeSignalDisplay
                maxSignals={5}
                showDetails={false}
                typeFilter={signalFilter}
                className="lg:col-span-1"
              />
              <PerformanceAnalyticsPanel
                timePeriod="day"
                className="lg:col-span-1"
              />
            </div>
          </div>
        )}

        {/* Strategy Builder Layout */}
        {activeLayout === 'builder' && (
          <div className="space-y-6">
            <StrategyBuilder
              onStrategyCreated={handleStrategyCreated}
              onClose={() => setActiveLayout('dashboard')}
            />
          </div>
        )}

        {/* Live Signals Layout */}
        {activeLayout === 'signals' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              <div className="xl:col-span-2">
                <RealTimeSignalDisplay
                  maxSignals={50}
                  showDetails={true}
                  typeFilter={signalFilter}
                  strategyFilter={selectedStrategy}
                />
              </div>
              <div className="xl:col-span-1">
                <PerformanceAnalyticsPanel
                  timePeriod="hour"
                  strategyId={selectedStrategy}
                />
              </div>
            </div>
          </div>
        )}

        {/* Analytics Layout */}
        {activeLayout === 'analytics' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <PerformanceAnalyticsPanel
                timePeriod="week"
                strategyId={selectedStrategy}
              />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <RealTimeSignalDisplay
                  maxSignals={20}
                  showDetails={false}
                  typeFilter={signalFilter}
                  strategyFilter={selectedStrategy}
                />
                <SignalEngineDashboard className="lg:col-span-1" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Strategy Builder Modal */}
      {showStrategyBuilder && activeLayout !== 'builder' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <StrategyBuilder
              onStrategyCreated={handleStrategyCreated}
              onClose={() => setShowStrategyBuilder(false)}
            />
          </div>
        </div>
      )}

      {/* Help/Info Panel */}
      <div className="fixed bottom-4 right-4 z-40">
        <div className="bg-white rounded-lg border border-gray-200 shadow-lg p-4 max-w-sm">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-lg">💡</span>
            <h4 className="font-medium text-gray-900">Quick Tips</h4>
          </div>
          <div className="text-sm text-gray-600 space-y-1">
            {activeLayout === 'dashboard' && (
              <>
                <p>• Monitor active strategies and recent signals</p>
                <p>• Check performance metrics in real-time</p>
                <p>• Use filters to focus on specific signal types</p>
              </>
            )}
            {activeLayout === 'builder' && (
              <>
                <p>• Choose between single or multi-indicator strategies</p>
                <p>• Configure indicator parameters carefully</p>
                <p>• Test strategies with historical data</p>
              </>
            )}
            {activeLayout === 'signals' && (
              <>
                <p>• Watch live signals as they're generated</p>
                <p>• Use pause/resume to control the feed</p>
                <p>• Check confidence levels for signal quality</p>
              </>
            )}
            {activeLayout === 'analytics' && (
              <>
                <p>• Analyze strategy performance over time</p>
                <p>• Compare different time periods</p>
                <p>• Monitor confidence distributions</p>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div>
              Signal Engine v1.0.0 • Advanced Trading Signal Generation
            </div>
            <div className="flex items-center space-x-4">
              <span>🎯 Signals</span>
              <span>📊 Analytics</span>
              <span>⚙️ Strategies</span>
              <span>📈 Performance</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignalEnginePage
