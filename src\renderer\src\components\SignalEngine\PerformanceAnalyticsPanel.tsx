/**
 * Performance Analytics Panel Component
 * Metrics displaying strategy performance, signal history, and success rates with charts
 */

import React, { useState, useEffect, useCallback } from 'react'
import { SignalEngine } from '../../../../main/core/SignalEngine'
import type { 
  SignalEnginePerformanceMetrics,
  GeneratedSignal,
  Strategy
} from '../../../../shared/types/signals'

/**
 * Performance analytics component props
 */
interface PerformanceAnalyticsPanelProps {
  /** Custom CSS classes */
  className?: string
  /** Strategy to analyze (if not provided, shows overall performance) */
  strategyId?: string
  /** Time period for analysis */
  timePeriod?: 'hour' | 'day' | 'week' | 'month' | 'all'
  /** Refresh interval in milliseconds */
  refreshInterval?: number
}

/**
 * Performance data interface
 */
interface PerformanceData {
  metrics: SignalEnginePerformanceMetrics | null
  signalHistory: GeneratedSignal[]
  strategies: Strategy[]
  timeSeriesData: TimeSeriesPoint[]
  isLoading: boolean
  error: string | null
}

/**
 * Time series data point
 */
interface TimeSeriesPoint {
  timestamp: number
  signalCount: number
  avgConfidence: number
  successRate: number
}

/**
 * Performance Analytics Panel Component
 */
export const PerformanceAnalyticsPanel: React.FC<PerformanceAnalyticsPanelProps> = ({
  className = '',
  strategyId,
  timePeriod = 'day',
  refreshInterval = 10000
}) => {
  // State management
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    metrics: null,
    signalHistory: [],
    strategies: [],
    timeSeriesData: [],
    isLoading: true,
    error: null
  })

  const [selectedMetric, setSelectedMetric] = useState<'signals' | 'confidence' | 'success'>('signals')

  /**
   * Get time period filter
   */
  const getTimePeriodMs = useCallback((): number => {
    const now = Date.now()
    switch (timePeriod) {
      case 'hour':
        return now - (60 * 60 * 1000)
      case 'day':
        return now - (24 * 60 * 60 * 1000)
      case 'week':
        return now - (7 * 24 * 60 * 60 * 1000)
      case 'month':
        return now - (30 * 24 * 60 * 60 * 1000)
      default:
        return 0
    }
  }, [timePeriod])

  /**
   * Load performance data
   */
  const loadPerformanceData = useCallback(async () => {
    try {
      setPerformanceData(prev => ({ ...prev, isLoading: true, error: null }))

      const signalEngine = SignalEngine.getInstance()
      
      // Get performance metrics
      const metrics = signalEngine.getPerformanceMetrics()
      
      // Get signal history
      const allSignals = signalEngine.getRecentSignals(1000) // Get more signals for analysis
      const timeFilter = getTimePeriodMs()
      const filteredSignals = timeFilter > 0 
        ? allSignals.filter(signal => signal.timestamp >= timeFilter)
        : allSignals

      // Filter by strategy if specified
      const signalHistory = strategyId 
        ? filteredSignals.filter(signal => signal.strategy === strategyId)
        : filteredSignals

      // Get active strategies
      const strategies = signalEngine.getActiveStrategies()

      // Generate time series data
      const timeSeriesData = generateTimeSeriesData(signalHistory, timePeriod)

      setPerformanceData({
        metrics,
        signalHistory,
        strategies,
        timeSeriesData,
        isLoading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load performance data'
      setPerformanceData(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
    }
  }, [strategyId, timePeriod, getTimePeriodMs])

  /**
   * Generate time series data for charts
   */
  const generateTimeSeriesData = (signals: GeneratedSignal[], period: string): TimeSeriesPoint[] => {
    if (signals.length === 0) return []

    const points: TimeSeriesPoint[] = []
    const now = Date.now()
    let intervalMs: number
    let pointCount: number

    // Determine interval and point count based on period
    switch (period) {
      case 'hour':
        intervalMs = 5 * 60 * 1000 // 5 minutes
        pointCount = 12
        break
      case 'day':
        intervalMs = 60 * 60 * 1000 // 1 hour
        pointCount = 24
        break
      case 'week':
        intervalMs = 24 * 60 * 60 * 1000 // 1 day
        pointCount = 7
        break
      case 'month':
        intervalMs = 24 * 60 * 60 * 1000 // 1 day
        pointCount = 30
        break
      default:
        intervalMs = 24 * 60 * 60 * 1000 // 1 day
        pointCount = 30
    }

    // Generate data points
    for (let i = pointCount - 1; i >= 0; i--) {
      const timestamp = now - (i * intervalMs)
      const endTime = timestamp + intervalMs
      
      const periodSignals = signals.filter(s => 
        s.timestamp >= timestamp && s.timestamp < endTime
      )

      const signalCount = periodSignals.length
      const avgConfidence = periodSignals.length > 0
        ? periodSignals.reduce((sum, s) => sum + s.confidence, 0) / periodSignals.length
        : 0

      // Calculate success rate (simplified - in real implementation, you'd need actual trade outcomes)
      const successRate = periodSignals.length > 0
        ? periodSignals.filter(s => s.confidence > 0.6).length / periodSignals.length
        : 0

      points.push({
        timestamp,
        signalCount,
        avgConfidence,
        successRate
      })
    }

    return points
  }

  /**
   * Calculate signal distribution
   */
  const getSignalDistribution = useCallback(() => {
    const { signalHistory } = performanceData
    const total = signalHistory.length
    
    if (total === 0) {
      return { buy: 0, sell: 0, hold: 0 }
    }

    const buy = signalHistory.filter(s => s.signal === 'BUY').length
    const sell = signalHistory.filter(s => s.signal === 'SELL').length
    const hold = signalHistory.filter(s => s.signal === 'HOLD').length

    return {
      buy: (buy / total) * 100,
      sell: (sell / total) * 100,
      hold: (hold / total) * 100
    }
  }, [performanceData.signalHistory])

  /**
   * Calculate confidence distribution
   */
  const getConfidenceDistribution = useCallback(() => {
    const { signalHistory } = performanceData
    const total = signalHistory.length
    
    if (total === 0) {
      return { high: 0, medium: 0, low: 0 }
    }

    const high = signalHistory.filter(s => s.confidence >= 0.7).length
    const medium = signalHistory.filter(s => s.confidence >= 0.4 && s.confidence < 0.7).length
    const low = signalHistory.filter(s => s.confidence < 0.4).length

    return {
      high: (high / total) * 100,
      medium: (medium / total) * 100,
      low: (low / total) * 100
    }
  }, [performanceData.signalHistory])

  /**
   * Get strategy performance summary
   */
  const getStrategyPerformance = useCallback(() => {
    const { signalHistory } = performanceData
    const strategyStats = new Map<string, {
      count: number
      avgConfidence: number
      lastSignal: number
    }>()

    signalHistory.forEach(signal => {
      const existing = strategyStats.get(signal.strategy) || {
        count: 0,
        avgConfidence: 0,
        lastSignal: 0
      }

      existing.count++
      existing.avgConfidence = (existing.avgConfidence * (existing.count - 1) + signal.confidence) / existing.count
      existing.lastSignal = Math.max(existing.lastSignal, signal.timestamp)

      strategyStats.set(signal.strategy, existing)
    })

    return Array.from(strategyStats.entries()).map(([name, stats]) => ({
      name,
      ...stats
    })).sort((a, b) => b.count - a.count)
  }, [performanceData.signalHistory])

  // Load data on mount and set up refresh interval
  useEffect(() => {
    loadPerformanceData()

    const interval = setInterval(loadPerformanceData, refreshInterval)
    return () => clearInterval(interval)
  }, [loadPerformanceData, refreshInterval])

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp)
    switch (timePeriod) {
      case 'hour':
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      case 'day':
        return date.toLocaleTimeString([], { hour: '2-digit' })
      case 'week':
      case 'month':
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
      default:
        return date.toLocaleDateString()
    }
  }

  /**
   * Render loading state
   */
  if (performanceData.isLoading && !performanceData.metrics) {
    return (
      <div className={`performance-analytics-panel ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading Performance Analytics...</span>
        </div>
      </div>
    )
  }

  /**
   * Render error state
   */
  if (performanceData.error) {
    return (
      <div className={`performance-analytics-panel ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 text-xl mr-2">⚠️</span>
            <div>
              <h3 className="text-red-800 font-medium">Analytics Error</h3>
              <p className="text-red-600 text-sm mt-1">{performanceData.error}</p>
            </div>
          </div>
          <button
            onClick={loadPerformanceData}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const signalDistribution = getSignalDistribution()
  const confidenceDistribution = getConfidenceDistribution()
  const strategyPerformance = getStrategyPerformance()

  return (
    <div className={`performance-analytics-panel bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <span className="mr-2">📊</span>
            Performance Analytics
            {strategyId && (
              <span className="ml-2 text-sm font-normal text-gray-500">
                • {strategyId}
              </span>
            )}
          </h3>
          <div className="flex items-center space-x-2">
            <select
              value={timePeriod}
              onChange={(e) => window.location.reload()} // Simplified - in real app, would update props
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="hour">Last Hour</option>
              <option value="day">Last Day</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        {performanceData.metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Total Signals</p>
                  <p className="text-2xl font-bold text-blue-900">
                    {performanceData.signalHistory.length}
                  </p>
                </div>
                <div className="text-2xl">🎯</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Avg Confidence</p>
                  <p className="text-2xl font-bold text-green-900">
                    {performanceData.signalHistory.length > 0
                      ? (performanceData.signalHistory.reduce((sum, s) => sum + s.confidence, 0) / performanceData.signalHistory.length * 100).toFixed(1)
                      : '0.0'
                    }%
                  </p>
                </div>
                <div className="text-2xl">📈</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Active Strategies</p>
                  <p className="text-2xl font-bold text-purple-900">
                    {performanceData.strategies.length}
                  </p>
                </div>
                <div className="text-2xl">⚙️</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">Avg Processing</p>
                  <p className="text-2xl font-bold text-orange-900">
                    {performanceData.metrics.averageProcessingTime.toFixed(1)}ms
                  </p>
                </div>
                <div className="text-2xl">⚡</div>
              </div>
            </div>
          </div>
        )}

        {/* Simple Performance Summary */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Signal Distribution */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4">Signal Distribution</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span className="text-sm text-gray-600">BUY Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${signalDistribution.buy}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {signalDistribution.buy.toFixed(1)}%
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="text-sm text-gray-600">SELL Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{ width: `${signalDistribution.sell}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {signalDistribution.sell.toFixed(1)}%
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span className="text-sm text-gray-600">HOLD Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-500 h-2 rounded-full"
                      style={{ width: `${signalDistribution.hold}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {signalDistribution.hold.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Strategy Performance */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4">Top Strategies</h4>
            {strategyPerformance.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">📊</div>
                <p className="text-gray-500">No strategy data available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {strategyPerformance.slice(0, 5).map((strategy, index) => (
                  <div key={strategy.name} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{strategy.name}</div>
                      <div className="text-sm text-gray-500">{strategy.count} signals</div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${
                        strategy.avgConfidence >= 0.7 ? 'text-green-600' :
                        strategy.avgConfidence >= 0.5 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {(strategy.avgConfidence * 100).toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-500">confidence</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Confidence Distribution */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-4">Confidence Distribution</h4>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {confidenceDistribution.high.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">High (≥70%)</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${confidenceDistribution.high}%` }}
                ></div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {confidenceDistribution.medium.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Medium (40-70%)</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-yellow-500 h-2 rounded-full"
                  style={{ width: `${confidenceDistribution.medium}%` }}
                ></div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {confidenceDistribution.low.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Low (<40%)</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-red-500 h-2 rounded-full"
                  style={{ width: `${confidenceDistribution.low}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformanceAnalyticsPanel
