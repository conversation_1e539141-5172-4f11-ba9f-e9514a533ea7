/**
 * Signal Persistence Manager Implementation
 * Comprehensive system for saving and loading signal history, strategy configurations, and performance data
 */

import { EventEmitter } from 'events'
import { promises as fs } from 'fs'
import { join, dirname } from 'path'
import { createHash } from 'crypto'
import { gzip, gunzip } from 'zlib'
import { promisify } from 'util'
import { logger } from '../../shared/utils/logger'
import {
  DEFAULT_PERSISTENCE_CONFIG,
  DEFAULT_BATCH_CONFIG,
  PERSISTENCE_ERROR_MESSAGES,
  PERSISTENCE_EVENTS,
  STORAGE_LIMITS,
  PERFORMANCE_SETTINGS
} from '../../shared/constants/persistence'
import type {
  PersistenceManager,
  PersistenceConfig,
  PersistenceResult,
  StoredSignal,
  StoredStrategy,
  SignalQuery,
  StrategyQuery,
  StorageStats,
  PerformanceSnapshot,
  PersistenceEvent,
  BatchConfig
} from '../../shared/types/persistence'
import type { GeneratedSignal, StrategyConfig } from '../../shared/types/signals'

// Promisify compression functions
const gzipAsync = promisify(gzip)
const gunzipAsync = promisify(gunzip)

/**
 * File-based persistence manager implementation
 */
export class FilePersistenceManager extends EventEmitter implements PersistenceManager {
  private _config: PersistenceConfig = DEFAULT_PERSISTENCE_CONFIG
  private _isInitialized: boolean = false
  private _signalIndex: Map<string, StoredSignal> = new Map()
  private _strategyIndex: Map<string, StoredStrategy> = new Map()
  private _stats: StorageStats = this._createInitialStats()
  private _cleanupTimer?: NodeJS.Timeout
  private _backupTimer?: NodeJS.Timeout

  /**
   * Create a new file persistence manager
   */
  constructor() {
    super()
    logger.debug('PersistenceManager', 'File persistence manager created')
  }

  /**
   * Initialize persistence system
   */
  public async initialize(config: PersistenceConfig = DEFAULT_PERSISTENCE_CONFIG): Promise<PersistenceResult<boolean>> {
    const startTime = performance.now()
    
    try {
      this._config = { ...DEFAULT_PERSISTENCE_CONFIG, ...config }
      
      // Validate configuration
      this._validateConfig(this._config)
      
      // Create storage directories
      await this._createDirectories()
      
      // Load existing indexes
      await this._loadIndexes()
      
      // Start cleanup timer
      this._startCleanupTimer()
      
      // Start backup timer
      this._startBackupTimer()
      
      this._isInitialized = true
      
      const duration = performance.now() - startTime
      
      this._emitEvent('system-initialized', {
        config: this._config,
        duration
      })
      
      logger.info('PersistenceManager', `Persistence system initialized in ${duration.toFixed(2)}ms`)
      
      return {
        success: true,
        data: true,
        metadata: { duration, recordsAffected: 0 }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('PersistenceManager', `Initialization failed: ${errorMessage}`)
      
      this._emitEvent('error-occurred', {
        operation: 'initialize',
        error: errorMessage
      })
      
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Close persistence system
   */
  public async close(): Promise<PersistenceResult<boolean>> {
    const startTime = performance.now()
    
    try {
      // Clear timers
      if (this._cleanupTimer) {
        clearInterval(this._cleanupTimer)
        this._cleanupTimer = undefined
      }
      
      if (this._backupTimer) {
        clearInterval(this._backupTimer)
        this._backupTimer = undefined
      }
      
      // Save indexes
      await this._saveIndexes()
      
      // Clear memory
      this._signalIndex.clear()
      this._strategyIndex.clear()
      
      this._isInitialized = false
      
      const duration = performance.now() - startTime
      
      logger.info('PersistenceManager', `Persistence system closed in ${duration.toFixed(2)}ms`)
      
      return {
        success: true,
        data: true,
        metadata: { duration }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('PersistenceManager', `Close failed: ${errorMessage}`)
      
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Save a signal
   */
  public async saveSignal(
    signal: GeneratedSignal,
    metadata?: Record<string, unknown>
  ): Promise<PersistenceResult<string>> {
    const startTime = performance.now()
    
    try {
      this._ensureInitialized()
      
      // Create stored signal
      const storedSignal: StoredSignal = {
        ...signal,
        id: this._generateId('signal'),
        storedAt: Date.now(),
        metadata: {
          source: 'signal-engine',
          ...metadata
        }
      }
      
      // Save to file
      const filePath = this._getSignalFilePath(storedSignal.id)
      await this._saveToFile(filePath, storedSignal)
      
      // Update index
      this._signalIndex.set(storedSignal.id, storedSignal)
      
      // Update stats
      this._stats.totalSignals++
      this._updateStats('write', performance.now() - startTime)
      
      const duration = performance.now() - startTime
      
      this._emitEvent('signal-saved', {
        signalId: storedSignal.id,
        strategy: signal.strategy,
        signal: signal.signal,
        duration
      })
      
      logger.debug('PersistenceManager', `Signal saved: ${storedSignal.id} in ${duration.toFixed(2)}ms`)
      
      return {
        success: true,
        data: storedSignal.id,
        metadata: { duration, recordsAffected: 1 }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('PersistenceManager', `Save signal failed: ${errorMessage}`)
      
      this._emitEvent('error-occurred', {
        operation: 'saveSignal',
        error: errorMessage
      })
      
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Save multiple signals
   */
  public async saveSignals(
    signals: GeneratedSignal[],
    metadata?: Record<string, unknown>[]
  ): Promise<PersistenceResult<string[]>> {
    const startTime = performance.now()
    
    try {
      this._ensureInitialized()
      
      const batchConfig = DEFAULT_BATCH_CONFIG
      const savedIds: string[] = []
      
      // Process in batches
      for (let i = 0; i < signals.length; i += batchConfig.batchSize) {
        const batch = signals.slice(i, i + batchConfig.batchSize)
        const batchMetadata = metadata?.slice(i, i + batchConfig.batchSize)
        
        const batchPromises = batch.map(async (signal, index) => {
          const result = await this.saveSignal(signal, batchMetadata?.[index])
          if (result.success && result.data) {
            savedIds.push(result.data)
          }
          return result
        })
        
        await Promise.all(batchPromises)
      }
      
      const duration = performance.now() - startTime
      
      logger.info('PersistenceManager', `Batch saved ${savedIds.length} signals in ${duration.toFixed(2)}ms`)
      
      return {
        success: true,
        data: savedIds,
        metadata: { duration, recordsAffected: savedIds.length }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('PersistenceManager', `Batch save signals failed: ${errorMessage}`)
      
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Load signals by query
   */
  public async loadSignals(query: SignalQuery): Promise<PersistenceResult<StoredSignal[]>> {
    const startTime = performance.now()
    
    try {
      this._ensureInitialized()
      
      // Apply filters
      let results = Array.from(this._signalIndex.values())
      
      // Strategy filter
      if (query.strategy) {
        results = results.filter(s => s.strategy === query.strategy)
      }
      
      // Signal type filter
      if (query.signalType) {
        results = results.filter(s => s.signal === query.signalType)
      }
      
      // Date range filter
      if (query.dateRange) {
        results = results.filter(s => 
          s.timestamp >= query.dateRange!.start && 
          s.timestamp <= query.dateRange!.end
        )
      }
      
      // Confidence range filter
      if (query.confidenceRange) {
        results = results.filter(s => 
          s.confidence >= query.confidenceRange!.min && 
          s.confidence <= query.confidenceRange!.max
        )
      }
      
      // Tags filter
      if (query.tags && query.tags.length > 0) {
        results = results.filter(s => 
          query.tags!.some(tag => s.metadata.tags?.includes(tag))
        )
      }
      
      // Sort results
      if (query.sortBy) {
        results.sort((a, b) => {
          let aValue: any, bValue: any
          
          switch (query.sortBy) {
            case 'timestamp':
              aValue = a.timestamp
              bValue = b.timestamp
              break
            case 'confidence':
              aValue = a.confidence
              bValue = b.confidence
              break
            case 'strategy':
              aValue = a.strategy
              bValue = b.strategy
              break
            default:
              return 0
          }
          
          if (query.sortOrder === 'desc') {
            return bValue > aValue ? 1 : bValue < aValue ? -1 : 0
          } else {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
          }
        })
      }
      
      // Apply pagination
      const offset = query.offset || 0
      const limit = Math.min(query.limit || STORAGE_LIMITS.maxQueryResults, STORAGE_LIMITS.maxQueryResults)
      const paginatedResults = results.slice(offset, offset + limit)
      
      const duration = performance.now() - startTime
      this._updateStats('read', duration)
      
      this._emitEvent('signal-loaded', {
        query,
        resultCount: paginatedResults.length,
        duration
      })
      
      logger.debug('PersistenceManager', `Loaded ${paginatedResults.length} signals in ${duration.toFixed(2)}ms`)
      
      return {
        success: true,
        data: paginatedResults,
        metadata: { duration, recordsAffected: paginatedResults.length }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.error('PersistenceManager', `Load signals failed: ${errorMessage}`)
      
      this._emitEvent('error-occurred', {
        operation: 'loadSignals',
        error: errorMessage
      })
      
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Load signal by ID
   */
  public async loadSignal(id: string): Promise<PersistenceResult<StoredSignal>> {
    const startTime = performance.now()

    try {
      this._ensureInitialized()

      const signal = this._signalIndex.get(id)
      if (!signal) {
        return {
          success: false,
          error: PERSISTENCE_ERROR_MESSAGES.RECORD_NOT_FOUND,
          metadata: { duration: performance.now() - startTime }
        }
      }

      const duration = performance.now() - startTime
      this._updateStats('read', duration)

      return {
        success: true,
        data: signal,
        metadata: { duration, recordsAffected: 1 }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Delete signal by ID
   */
  public async deleteSignal(id: string): Promise<PersistenceResult<boolean>> {
    const startTime = performance.now()

    try {
      this._ensureInitialized()

      const signal = this._signalIndex.get(id)
      if (!signal) {
        return {
          success: false,
          error: PERSISTENCE_ERROR_MESSAGES.RECORD_NOT_FOUND,
          metadata: { duration: performance.now() - startTime }
        }
      }

      // Delete file
      const filePath = this._getSignalFilePath(id)
      await fs.unlink(filePath)

      // Remove from index
      this._signalIndex.delete(id)

      // Update stats
      this._stats.totalSignals--

      const duration = performance.now() - startTime

      this._emitEvent('signal-deleted', {
        signalId: id,
        duration
      })

      return {
        success: true,
        data: true,
        metadata: { duration, recordsAffected: 1 }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Delete signals by query
   */
  public async deleteSignals(query: SignalQuery): Promise<PersistenceResult<number>> {
    const startTime = performance.now()

    try {
      this._ensureInitialized()

      // Load signals matching query
      const loadResult = await this.loadSignals(query)
      if (!loadResult.success || !loadResult.data) {
        return {
          success: false,
          error: loadResult.error || 'Failed to load signals for deletion',
          metadata: { duration: performance.now() - startTime }
        }
      }

      // Delete each signal
      let deletedCount = 0
      for (const signal of loadResult.data) {
        const deleteResult = await this.deleteSignal(signal.id)
        if (deleteResult.success) {
          deletedCount++
        }
      }

      const duration = performance.now() - startTime

      return {
        success: true,
        data: deletedCount,
        metadata: { duration, recordsAffected: deletedCount }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }

  /**
   * Save a strategy
   */
  public async saveStrategy(
    strategy: StrategyConfig,
    metadata?: Record<string, unknown>
  ): Promise<PersistenceResult<string>> {
    const startTime = performance.now()

    try {
      this._ensureInitialized()

      const storedStrategy: StoredStrategy = {
        id: this._generateId('strategy'),
        config: strategy,
        metadata: {
          createdAt: Date.now(),
          modifiedAt: Date.now(),
          version: '1.0.0',
          ...metadata
        }
      }

      // Save to file
      const filePath = this._getStrategyFilePath(storedStrategy.id)
      await this._saveToFile(filePath, storedStrategy)

      // Update index
      this._strategyIndex.set(storedStrategy.id, storedStrategy)

      // Update stats
      this._stats.totalStrategies++

      const duration = performance.now() - startTime

      this._emitEvent('strategy-saved', {
        strategyId: storedStrategy.id,
        name: strategy.name,
        duration
      })

      return {
        success: true,
        data: storedStrategy.id,
        metadata: { duration, recordsAffected: 1 }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return {
        success: false,
        error: errorMessage,
        metadata: { duration: performance.now() - startTime }
      }
    }
  }
